import React, { createContext, useContext, ReactNode } from 'react';
// Import the actual context type and the hook that provides the state
import { ProductionContextType } from '@/types/production-context';
import { useProductionState } from '@/hooks/use-production-state';

// We still need the context definition
const ProductionContext = createContext<ProductionContextType | undefined>(undefined);

// Props for the Provider component
interface ProductionProviderProps {
  children: ReactNode;
}

// The Provider component now USES the state hook
export const ProductionProvider: React.FC<ProductionProviderProps> = ({ children }) => {
  // Get the state and functions from the hook
  const productionState = useProductionState(); 

  // The context value is now simply the object returned by the hook
  // Assign directly, assuming the hook returns a compatible type (fix hook if needed)
  const contextValue = productionState as ProductionContextType; // Use type assertion for now

  return (
    // Provide the state from the hook to the context
    <ProductionContext.Provider value={contextValue}>
      {children}
    </ProductionContext.Provider>
  );
};

// Custom hook to use the context remains the same
export const useProduction = () => {
  const context = useContext(ProductionContext);
  if (context === undefined) {
    throw new Error('useProduction must be used within a ProductionProvider');
  }
  return context;
};

