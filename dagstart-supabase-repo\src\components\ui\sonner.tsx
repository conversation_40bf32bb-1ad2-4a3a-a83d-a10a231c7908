
import { Toaster as SonnerToaster } from "sonner";

type ToastProps = React.ComponentProps<typeof SonnerToaster>;

function Toaster({ ...props }: ToastProps) {
  return (
    <SonnerToaster
      theme="light"
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
        duration: 5000,
        closeButton: true,
      }}
      {...props}
    />
  );
}

export { Toaster };
