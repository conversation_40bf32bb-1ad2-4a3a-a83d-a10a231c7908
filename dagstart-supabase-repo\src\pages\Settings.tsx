import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Edit2, Save, X, Clock, History as HistoryIcon, DownloadIcon,
  ChevronDown, Filter, Calendar, AlertCircle, Package, Info, Plus, Trash2, Target, RotateCcw,
  Calendar as CalendarIcon,
  Download,
  TrendingUp,
  Activity,
  FileSpreadsheet,
  BarChart3,
  ListFilter,
  ArrowDownToLine,
  PieChart,
  AlertTriangle,
  PlusCircle,
  Pencil,
  BrainCircuit, // Added AI icon
  Settings as SettingsIcon // Added Settings icon
} from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProduction } from '@/context/ProductionContext';
import toast from '@/hooks/use-toast';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { loadItem, saveItem } from '@/lib/local-storage';
import { ProductionLine, EquipmentEntry } from '@/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Link } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav';
import { Label } from "@/components/ui/label";
import { Textarea } from '@/components/ui/textarea'; // Import Textarea
import AiSettingsDialog from '@/components/settings/AiSettingsDialog'; // Import AI Settings Dialog

type TargetState = {
  production: string;
  yield: string;
};

type TargetsState = {
  tl1: TargetState;
  tl2: TargetState;
  p1: TargetState;
  p2: TargetState;
  p3: TargetState;
};

type LineFilter = ProductionLine | 'all';
type HistoryLineFilter = ProductionLine | 'all';

// Nieuwe types voor GeschiedenisV2
type ViewMode = 'table' | 'calendar' | 'chart' | 'timeline';
type ChartType = 'production' | 'yield' | 'disruptions';

// AI Settings Type
type AiSettings = {
  enabled: boolean;
  apiKey: string;
  prompt: string;
};

const Settings: React.FC = () => {
  const {
    updateTargets,
    updateYieldTargets,
    TARGETS,
    YIELD_TARGETS,
    historyData,
    productionData,
    materialOptions,
    addMaterialOption,
    removeMaterialOption,
    equipmentOptions,
    addEquipmentArea,
    removeEquipmentArea,
    addEquipmentOption,
    removeEquipmentOption,
    isFlyLocked,
    setIsFlyLocked,
    archiveAndClearIncidents,
  } = useProduction();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('targets'); // Default to 'targets'
  const [activeLineTab, setActiveLineTab] = useState<ProductionLine>('tl1');
  const [editMode, setEditMode] = useState(false);

  // Nieuwe state voor Geschiedenis2
  const [selectedLine, setSelectedLine] = useState<LineFilter>('all');
  const [showOnlyDisruptions, setShowOnlyDisruptions] = useState(false);
  const [dateFilter, setDateFilter] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [historySelectedLine, setHistorySelectedLine] = useState<HistoryLineFilter>('all');
  const [showOnlyBelowTarget, setShowOnlyBelowTarget] = useState(false);
  // Nieuwe state voor saveVK toggle
  const [saveVK, setSaveVK] = useState<boolean>(() => {
    return localStorage.getItem('saveVK') === 'true';
  });

  // State for AI Settings
  const [aiSettings, setAiSettings] = useState<AiSettings>(() => {
    return loadItem<AiSettings>('aiSettings', { enabled: false, apiKey: '', prompt: '' }) || { enabled: false, apiKey: '', prompt: '' };
  });
  const [aiSettingsDialogOpen, setAiSettingsDialogOpen] = useState(false); // State for AI settings dialog

  // Effect om saveVK in localStorage op te slaan wanneer het verandert
  useEffect(() => {
    localStorage.setItem('saveVK', saveVK.toString());
  }, [saveVK]);

  // Effect to save AI settings to localStorage
  useEffect(() => {
    saveItem('aiSettings', aiSettings);
  }, [aiSettings]);


  const [targets, setTargets] = useState<TargetsState>(() => {
    const initialTargets: TargetsState = {
      tl1: { production: (TARGETS.tl1 * 3).toString(), yield: YIELD_TARGETS.tl1.toString() },
      tl2: { production: (TARGETS.tl2 * 3).toString(), yield: YIELD_TARGETS.tl2.toString() },
      p1: { production: (TARGETS.p1 * 3).toString(), yield: YIELD_TARGETS.p1.toString() },
      p2: { production: (TARGETS.p2 * 3).toString(), yield: YIELD_TARGETS.p2.toString() },
      p3: { production: (TARGETS.p3 * 3).toString(), yield: YIELD_TARGETS.p3.toString() }
    };
    return initialTargets;
  });

  const [tempTargets, setTempTargets] = useState<TargetsState>(targets);

  useEffect(() => {
    const updatedTargets: TargetsState = {
      tl1: { production: (TARGETS.tl1 * 3).toString(), yield: YIELD_TARGETS.tl1.toString() },
      tl2: { production: (TARGETS.tl2 * 3).toString(), yield: YIELD_TARGETS.tl2.toString() },
      p1: { production: (TARGETS.p1 * 3).toString(), yield: YIELD_TARGETS.p1.toString() },
      p2: { production: (TARGETS.p2 * 3).toString(), yield: YIELD_TARGETS.p2.toString() },
      p3: { production: (TARGETS.p3 * 3).toString(), yield: YIELD_TARGETS.p3.toString() }
    };
    setTargets(updatedTargets);
    if (!editMode) {
      setTempTargets(updatedTargets);
    }
  }, [TARGETS, YIELD_TARGETS, editMode]);

  const productionLines: ProductionLine[] = ['tl1', 'tl2', 'p1', 'p2', 'p3'];

  const handleEdit = () => {
    setTempTargets(targets);
    setEditMode(true);
  };

  const handleCancel = () => {
    setTempTargets(targets);
    setEditMode(false);
  };

  const handleSave = () => {
    // Valideer en converteer alle waarden
    const newProductionTargets: Record<ProductionLine, number> = {} as Record<ProductionLine, number>;
    const newYieldTargets: Record<ProductionLine, number> = {} as Record<ProductionLine, number>;
    let hasError = false;

    productionLines.forEach(line => {
      const prodValue = parseFloat(tempTargets[line].production);
      const yieldValue = parseFloat(tempTargets[line].yield);

      if (isNaN(prodValue) || prodValue < 0) {
        toast.error(`Ongeldige productie target voor ${line.toUpperCase()}`);
        hasError = true;
      }
      if (isNaN(yieldValue) || yieldValue < 0 || yieldValue > 100) {
        toast.error(`Ongeldige yield target voor ${line.toUpperCase()} (moet tussen 0-100 zijn)`);
        hasError = true;
      }

      // Deel door 3 om de shift target op te slaan
      newProductionTargets[line] = prodValue / 3;
      newYieldTargets[line] = yieldValue;
    });

    if (!hasError) {
    updateTargets(newProductionTargets);
    updateYieldTargets(newYieldTargets);
      setTargets(tempTargets);
    setEditMode(false);
      toast.success("Targets opgeslagen");
    }
  };

  const handleExportDataToJson = () => {
    // Corrected: Use historyData directly
    const jsonStr = JSON.stringify(historyData, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `production-history-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success("Geschiedenis geëxporteerd als JSON");
  };

  // Voeg nieuwe functie toe om geschiedenis te groeperen per lijn
  const historyByLine = useMemo(() => {
    const result: Record<ProductionLine, typeof historyData> = {
      tl1: [], tl2: [], p1: [], p2: [], p3: []
    };

    historyData.forEach(entry => {
      if (entry.line) {
        result[entry.line].push(entry);
      }
    });

    Object.keys(result).forEach(line => {
      result[line as ProductionLine].sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    });

    return result;
  }, [historyData]);

  // Nieuwe state voor Geschiedenis2
  const [filteredHistory, setFilteredHistory] = useState<typeof historyData>([]);

  // Nieuwe functie voor het filteren van geschiedenis op datum
  const getDateFilter = (filter: '7d' | '30d' | '90d' | 'all'): Date | null => {
    if (filter === 'all') return null;

    const now = new Date();
    const days = filter === '7d' ? 7 : filter === '30d' ? 30 : 90;
    return new Date(now.setDate(now.getDate() - days));
  };

  const filteredHistoryData = useMemo(() => {
    let filtered = [...historyData];

    // Filter op productielijn
    if (historySelectedLine !== 'all') {
      filtered = filtered.filter(entry => {
        // Check zowel production als breakdowns data in het nieuwe formaat
        const lineData = entry.data[historySelectedLine];
        return lineData && (lineData.rows.length > 0 || lineData.disruptions.length > 0);
      });
    }

    // Filter op datum
    const dateFilterStart = getDateFilter(dateFilter);
    if (dateFilterStart) {
      filtered = filtered.filter(entry => {
        const entryDate = new Date(entry.timestamp);
        return entryDate >= dateFilterStart;
      });
    }

    // Filter op storingen
    if (showOnlyDisruptions) {
      filtered = filtered.filter(entry => {
        if (historySelectedLine !== 'all') {
          const lineData = entry.data[historySelectedLine];
          return lineData?.disruptions && lineData.disruptions.length > 0;
        } else {
          // Check alle lijnen voor storingen
          return productionLines.some(line => {
            const lineData = entry.data[line];
            return lineData?.disruptions && lineData.disruptions.length > 0;
          });
        }
      });
    }

    // Sorteer op datum (nieuwste eerst)
    filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return filtered;
  }, [historyData, historySelectedLine, dateFilter, showOnlyDisruptions, productionLines]);

  // Statistieken berekeningen
  const calculateStatistics = useMemo(() => {
    const initialStats = {
      totalProduction: 0,
      yieldCounts: [] as number[],
      totalDowntime: 0
    };

    return filteredHistoryData.reduce((stats, entry) => {
      const relevantLines = historySelectedLine === 'all' ? productionLines : [historySelectedLine];

      if (!entry || !entry.data) {
        console.warn("Skipping history entry with missing or undefined data:", entry);
        return stats;
      }

      relevantLines.forEach(line => {
        const lineData = entry.data[line];
        if (lineData) {
          lineData.rows?.forEach(row => {
            stats.totalProduction +=
              (Number(row.od.production) || 0) +
              (Number(row.md.production) || 0) +
              (Number(row.nd.production) || 0);

            if (row.od.yield) stats.yieldCounts.push(Number(row.od.yield));
            if (row.md.yield) stats.yieldCounts.push(Number(row.md.yield));
            if (row.nd.yield) stats.yieldCounts.push(Number(row.nd.yield));
          });

          lineData.disruptions?.forEach(disruption => {
            const [hours, minutes] = (disruption.duration || "0:00").split(":").map(Number);
            stats.totalDowntime += hours * 60 + minutes;
          });
        }
      });

      return stats;
    }, initialStats);
  }, [filteredHistoryData, historySelectedLine, productionLines]);

  // State voor de dialogs
  const [showMaterialDialog, setShowMaterialDialog] = useState(false);
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [showEquipmentDialog, setShowEquipmentDialog] = useState(false);

  // State voor nieuwe items in dialogs
  const [newMaterial, setNewMaterial] = useState('');
  const [selectedLines, setSelectedLines] = useState<ProductionLine[]>([]);
  const [materialTypes, setMaterialTypes] = useState<string[]>([]);
  const [newSectionName, setNewSectionName] = useState('');
  const [newEquipmentName, setNewEquipmentName] = useState('');
  const [selectedSectionName, setSelectedSectionName] = useState<string>('');

  // Helper om sectienamen voor de ACTIEVE lijn te krijgen
  const activeLineSections = useMemo(() => {
    return Object.keys(equipmentOptions[activeLineTab] || {}).sort();
  }, [equipmentOptions, activeLineTab]);

  // Functies voor materiaal beheer
  const handleAddMaterial = useCallback(() => {
    if (!newMaterial.trim()) {
      toast.error("Materiaal naam kan niet leeg zijn");
      return;
    }

    if (selectedLines.length === 0) {
      toast.error("Selecteer ten minste één productielijn");
      return;
    }

    // Eerste letter hoofdletter, rest kleine letters
    const formattedMaterial = newMaterial.charAt(0).toUpperCase() + newMaterial.slice(1).toLowerCase();

    // Speciale logica voor TL1 en TL2
    const hasTL1 = selectedLines.includes('tl1');
    const hasTL2 = selectedLines.includes('tl2');
    const hasS = materialTypes.includes('S');
    const hasT = materialTypes.includes('T');

    // Filter andere lijnen dan TL1 en TL2
    const otherLines = selectedLines.filter(line => line !== 'tl1' && line !== 'tl2');

    // Voeg materialen toe aan TL1 en TL2 volgens de regels
    if (hasTL1 && hasS) {
      addMaterialOption('tl1', `${formattedMaterial} S`);
    }
    if (hasTL2 && hasT) {
      addMaterialOption('tl2', `${formattedMaterial} T`);
    }

    // Voor andere lijnen, voeg materialen toe zoals normaal
    if (otherLines.length > 0) {
      otherLines.forEach(line => {
        if (materialTypes.length === 0) {
          addMaterialOption(line, formattedMaterial);
        } else {
          materialTypes.forEach(type => {
            addMaterialOption(line, `${formattedMaterial} ${type}`);
          });
        }
      });
    }

    // Reset form
    setNewMaterial('');
    setSelectedLines([]);
    setMaterialTypes([]);
    setShowMaterialDialog(false);

    toast.success(`Materiaal "${formattedMaterial}" is toegevoegd aan de geselecteerde lijnen`);
  }, [addMaterialOption, newMaterial, selectedLines, materialTypes]);

  const handleDeleteMaterial = useCallback((line: ProductionLine, material: string) => {
    removeMaterialOption(line, material);
  }, [removeMaterialOption]);

  // Functies voor sectie beheer
  const handleAddSection = useCallback(() => {
    const trimmedName = newSectionName.trim();
    if (!trimmedName) {
      toast.error("Gebied beschrijving is verplicht.");
      return;
    }
    addEquipmentArea(activeLineTab, trimmedName, trimmedName);
    setNewSectionName('');
    setShowSectionDialog(false);
  }, [newSectionName, activeLineTab, addEquipmentArea]);

  const handleDeleteSection = useCallback((sectionName: string) => {
    removeEquipmentArea(activeLineTab, sectionName);
  }, [activeLineTab, removeEquipmentArea]);

  // Functies voor apparaat beheer
  const handleAddEquipment = useCallback(() => {
    const trimmedName = newEquipmentName.trim();
    if (!trimmedName || !selectedSectionName) {
      toast.error('Selecteer een gebied en vul een naam in.');
      return;
    }
    const newEquipment: EquipmentEntry = {
      id: crypto.randomUUID(),
      label_nl: trimmedName,
      label_en: '',
      value: trimmedName,
    };
    addEquipmentOption(activeLineTab, selectedSectionName, newEquipment);
    setNewEquipmentName('');
    setSelectedSectionName('');
    setShowEquipmentDialog(false);
  }, [newEquipmentName, selectedSectionName, activeLineTab, addEquipmentOption]);

  const handleDeleteEquipment = useCallback((sectionName: string, equipmentId: string) => {
    removeEquipmentOption(activeLineTab, sectionName, equipmentId);
  }, [activeLineTab, removeEquipmentOption]);

  const handleLineChange = (value: string) => {
    if (value === 'all' || value === 'tl1' || value === 'tl2' || value === 'p1' || value === 'p2' || value === 'p3') {
      setSelectedLine(value as LineFilter);
    }
  };

  const handleHistoryLineChange = (value: string) => {
    if (value === 'all' || value === 'tl1' || value === 'tl2' || value === 'p1' || value === 'p2' || value === 'p3') {
      setHistorySelectedLine(value);
    }
  };

  // Expliciete handler voor lijn selectie
  const handleActiveLineChange = (value: string) => {
    // We weten dat value een van de ProductionLine types is
    setActiveLineTab(value as ProductionLine);
  };

  // Handler for AI settings changes
  const handleAiSettingChange = (field: keyof AiSettings, value: string | boolean) => {
    const newSettings = { ...aiSettings, [field]: value };
    setAiSettings(newSettings);
    // If enabling AI and API key is missing, open the dialog
    if (field === 'enabled' && value === true && !newSettings.apiKey) {
      setAiSettingsDialogOpen(true);
    }
  };

  // Handler for saving AI settings from the dialog
  const handleSaveAiSettings = (newSettings: AiSettings) => {
    setAiSettings(newSettings);
    // Optionally add a toast message
    toast.success("AI instellingen opgeslagen.");
  };


  return (
    <div className="animate-fade-in">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-faerch-blue">Instellingen</h1>
      </div>

      {/* Added defaultValue to potentially fix double-click issue */}
      <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <SettingsSubNav />

        <TabsContent value="targets">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Productie Targets & Yields</h2>
              <div className="space-x-2">
                {editMode ? (
                  <>
                    <Button variant="outline" size="sm" onClick={handleCancel} disabled={isFlyLocked}>
                      <X className="h-4 w-4 mr-1" />
                      Annuleren
                    </Button>
                    <Button size="sm" onClick={handleSave} disabled={isFlyLocked}>
                      <Save className="h-4 w-4 mr-1" />
                      Opslaan
                    </Button>
                  </>
                ) : (
                  <Button size="sm" onClick={handleEdit} disabled={isFlyLocked}>
                    <Edit2 className="h-4 w-4 mr-1" />
                    Bewerken
                  </Button>
                )}
              </div>
            </div>

            <Tabs value={activeLineTab} onValueChange={handleActiveLineChange} className="space-y-4">
              <TabsList className="grid grid-cols-5 gap-4">
                {productionLines.map(line => (
                  <TabsTrigger
                    key={line}
                    value={line}
                    className="flex-1"
                  >
                    {line.toUpperCase()}
                  </TabsTrigger>
                ))}
              </TabsList>
              {productionLines.map(line => (
                <TabsContent key={line} value={line}>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`${line}-production`}>Productie Target (per dag)</Label>
                      <Input
                        id={`${line}-production`}
                        type="number"
                        value={editMode ? tempTargets[line].production : targets[line].production}
                        onChange={(e) => editMode && setTempTargets(prev => ({
                          ...prev,
                          [line]: { ...prev[line], production: e.target.value }
                        }))}
                        readOnly={!editMode}
                        className={!editMode ? "bg-gray-100" : ""}
                      />
                    </div>
                    <div>
                      <Label htmlFor={`${line}-yield`}>Yield Target (%)</Label>
                      <Input
                        id={`${line}-yield`}
                        type="number"
                        min="0"
                        max="100"
                        value={editMode ? tempTargets[line].yield : targets[line].yield}
                        onChange={(e) => editMode && setTempTargets(prev => ({
                          ...prev,
                          [line]: { ...prev[line], yield: e.target.value }
                        }))}
                        readOnly={!editMode}
                        className={!editMode ? "bg-gray-100" : ""}
                      />
                    </div>
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </Card>
        </TabsContent>

        <TabsContent value="materials">
          <Card className="p-6">
            <Tabs defaultValue="beheer" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="beheer">Materiaal Beheer</TabsTrigger>
                <TabsTrigger value="vergelijking">Materiaal Vergelijking</TabsTrigger>
              </TabsList>

              <TabsContent value="beheer">
                <div className="flex justify-end mb-4">
                  <Dialog open={showMaterialDialog} onOpenChange={setShowMaterialDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm"><PlusCircle className="h-4 w-4 mr-1" /> Nieuw Materiaal</Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Nieuw Materiaal Toevoegen</DialogTitle>
                      </DialogHeader>
                      <div className="flex flex-col space-y-2">
                        <Label htmlFor="new-material-name">Materiaal Naam (zonder S/T)</Label>
                        <Input
                          id="new-material-name"
                          value={newMaterial}
                          onChange={(e) => setNewMaterial(e.target.value)}
                          placeholder="bv. Suezpmdx"
                        />
                      </div>
                      <div className="flex flex-col space-y-2">
                        <Label>Selecteer Productielijnen</Label>
                        <div className="grid grid-cols-3 gap-2">
                          {productionLines.map((line) => (
                            <label key={line} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={selectedLines.includes(line)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedLines([...selectedLines, line]);
                                  } else {
                                    setSelectedLines(selectedLines.filter(l => l !== line));
                                  }
                                }}
                              />
                              <span>{line.toUpperCase()}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                      <div className="flex flex-col space-y-2">
                        <Label>Selecteer Type (optioneel, voor TL1/TL2)</Label>
                        <div className="flex space-x-4">
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              value="S"
                              checked={materialTypes.includes('S')}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setMaterialTypes([...materialTypes, 'S']);
                                } else {
                                  setMaterialTypes(materialTypes.filter(t => t !== 'S'));
                                }
                              }}
                            />
                            <span>S</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              value="T"
                              checked={materialTypes.includes('T')}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setMaterialTypes([...materialTypes, 'T']);
                                } else {
                                  setMaterialTypes(materialTypes.filter(t => t !== 'T'));
                                }
                              }}
                            />
                            <span>T</span>
                          </label>
                        </div>
                      </div>
                      <Button onClick={handleAddMaterial} className="mt-4">Toevoegen</Button>
                    </DialogContent>
                  </Dialog>
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Lijn</TableHead>
                      <TableHead>Materiaal</TableHead>
                      <TableHead className="text-right">Acties</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(materialOptions).map(([line, materials]) => (
                      materials.map((material, index) => (
                        <TableRow key={`${line}-${index}`}>
                          <TableCell>{line.toUpperCase()}</TableCell>
                          <TableCell>{material}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="icon" onClick={() => handleDeleteMaterial(line as ProductionLine, material)} disabled={isFlyLocked}>
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="vergelijking">
                <p className="text-sm text-gray-600">
                  Vergelijkingsfunctionaliteit nog niet geïmplementeerd.
                </p>
                {/* Hier komt de vergelijkingslogica */}
              </TabsContent>
            </Tabs>
          </Card>
        </TabsContent>

        <TabsContent value="equipment">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Apparatuur Codes</h2>
              <div className="flex items-center space-x-4">
                <Select value={activeLineTab} onValueChange={handleActiveLineChange} disabled={isFlyLocked}>
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Lijn" />
                  </SelectTrigger>
                  <SelectContent>
                    {productionLines.map(line => (
                      <SelectItem key={line} value={line}>{line.toUpperCase()}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="space-x-2">
                  <Dialog open={showSectionDialog} onOpenChange={setShowSectionDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="outline" disabled={isFlyLocked}><Plus className="h-4 w-4 mr-1" /> Gebied</Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Nieuw Gebied Toevoegen ({activeLineTab.toUpperCase()})</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-2">
                        <Label htmlFor="new-section-name">Gebied Beschrijving</Label>
                        <Input
                          id="new-section-name"
                          value={newSectionName}
                          onChange={(e) => setNewSectionName(e.target.value)}
                          placeholder="bv. Extruder"
                        />
                      </div>
                      <Button onClick={handleAddSection} className="mt-4">Gebied Toevoegen</Button>
                    </DialogContent>
                  </Dialog>
                  <Dialog open={showEquipmentDialog} onOpenChange={setShowEquipmentDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm" disabled={isFlyLocked || activeLineSections.length === 0}><Plus className="h-4 w-4 mr-1" /> Apparaat</Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Nieuw Apparaat Toevoegen ({activeLineTab.toUpperCase()})</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-2">
                        <Label htmlFor="select-section">Selecteer Gebied</Label>
                        <Select value={selectedSectionName} onValueChange={setSelectedSectionName}>
                          <SelectTrigger id="select-section">
                            <SelectValue placeholder="Kies een gebied" />
                          </SelectTrigger>
                          <SelectContent>
                            {activeLineSections.map((name) => (
                              <SelectItem key={name} value={name}>{name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="new-equipment-name">Apparaat Naam/Code</Label>
                        <Input
                          id="new-equipment-name"
                          value={newEquipmentName}
                          onChange={(e) => setNewEquipmentName(e.target.value)}
                          placeholder="bv. EX-010"
                        />
                      </div>
                      <Button onClick={handleAddEquipment} className="mt-4" disabled={!selectedSectionName}>Apparaat Toevoegen</Button>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>

            <Accordion type="multiple" className="w-full">
              {activeLineSections.length === 0 ? (
                <p className="text-center text-gray-500 py-4">Geen gebieden gedefinieerd voor {activeLineTab.toUpperCase()}.</p>
              ) : (
                activeLineSections.map((sectionName) => (
                  <AccordionItem key={sectionName} value={sectionName}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex justify-between items-center w-full pr-4">
                        <span>{sectionName}</span>
                        <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => { e.stopPropagation(); handleDeleteSection(sectionName); }} disabled={isFlyLocked}>
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Naam/Code</TableHead>
                            <TableHead className="text-right">Acties</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {(() => {
                            const items = equipmentOptions[activeLineTab]?.[sectionName] || [];
                            if (items.length === 0) {
                              return <TableRow><TableCell colSpan={2} className="text-center italic text-gray-500">Geen apparaten in dit gebied.</TableCell></TableRow>;
                            }
                            return items.map((item) => (
                              <TableRow key={item.id}>
                                <TableCell>{item.label_nl} ({item.value})</TableCell>
                                <TableCell className="text-right">
                                  <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => handleDeleteEquipment(sectionName, item.id)} disabled={isFlyLocked}>
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ));
                          })()}
                        </TableBody>
                      </Table>
                    </AccordionContent>
                  </AccordionItem>
                ))
              )}
            </Accordion>
          </Card>
        </TabsContent>

        <TabsContent value="toggles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Algemene Toggles</CardTitle>
              <CardDescription>Beheer algemene applicatie instellingen.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="fly-lock" className="font-medium">Fly Lock (Voorkom wijzigingen)</Label>
                  <p className="text-xs text-gray-500">Indien actief, kunnen targets, materialen en apparatuur niet worden gewijzigd.</p>
                </div>
                <Switch
                  id="fly-lock"
                  checked={isFlyLocked}
                  onCheckedChange={setIsFlyLocked}
                />
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="save-vk" className="font-medium">V&K Meldingen Archiveren bij Verwijderen</Label>
                  <p className="text-xs text-gray-500">Sla V&K meldingen op in de geschiedenis wanneer ze worden verwijderd.</p>
                </div>
                <Switch
                  id="save-vk"
                  checked={saveVK}
                  onCheckedChange={setSaveVK}
                />
              </div>
            </CardContent>
          </Card>

          {/* AI Settings Card - Updated */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><BrainCircuit className="w-5 h-5 text-purple-600" /> AI Instellingen</CardTitle>
              <CardDescription>Configureer AI-assistentie voor storingsanalyse.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-grow">
                  <Label htmlFor="ai-enabled" className="font-medium">AI Assistentie Inschakelen</Label>
                  <p className="text-xs text-gray-500">Activeer AI hulp voor het analyseren van storingen.</p>
                </div>
                <div className="flex items-center space-x-2">
                   {aiSettings.enabled && (
                     <Button variant="outline" size="sm" onClick={() => setAiSettingsDialogOpen(true)}>
                       <SettingsIcon className="h-4 w-4 mr-1" />
                       Configureren
                     </Button>
                   )}
                   <Switch
                     id="ai-enabled"
                     checked={aiSettings.enabled}
                     onCheckedChange={(checked) => handleAiSettingChange('enabled', checked)}
                   />
                </div>
              </div>
              {/* Removed inline inputs for API Key and Prompt */}
            </CardContent>
          </Card>

        </TabsContent>
      </Tabs>

      {/* Material Dialog */}
      <Dialog open={showMaterialDialog} onOpenChange={setShowMaterialDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuw Materiaal Toevoegen</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col space-y-2">
            <Label htmlFor="new-material-name">Materiaal Naam (zonder S/T)</Label>
            <Input
              id="new-material-name"
              value={newMaterial}
              onChange={(e) => setNewMaterial(e.target.value)}
              placeholder="bv. Suezpmdx"
            />
          </div>
          <div className="flex flex-col space-y-2">
            <Label>Selecteer Productielijnen</Label>
            <div className="grid grid-cols-3 gap-2">
              {productionLines.map((line) => (
                <label key={line} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectedLines.includes(line)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedLines([...selectedLines, line]);
                      } else {
                        setSelectedLines(selectedLines.filter(l => l !== line));
                      }
                    }}
                  />
                  <span>{line.toUpperCase()}</span>
                </label>
              ))}
            </div>
          </div>
          <div className="flex flex-col space-y-2">
            <Label>Selecteer Type (optioneel, voor TL1/TL2)</Label>
            <div className="flex space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  value="S"
                  checked={materialTypes.includes('S')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setMaterialTypes([...materialTypes, 'S']);
                    } else {
                      setMaterialTypes(materialTypes.filter(t => t !== 'S'));
                    }
                  }}
                />
                <span>S</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  value="T"
                  checked={materialTypes.includes('T')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setMaterialTypes([...materialTypes, 'T']);
                    } else {
                      setMaterialTypes(materialTypes.filter(t => t !== 'T'));
                    }
                  }}
                />
                <span>T</span>
              </label>
            </div>
          </div>
          <Button onClick={handleAddMaterial} className="mt-4">Toevoegen</Button>
        </DialogContent>
      </Dialog>

      {/* AI Settings Dialog */}
      <AiSettingsDialog
        initialSettings={aiSettings}
        open={aiSettingsDialogOpen}
        onOpenChange={setAiSettingsDialogOpen}
        onSave={handleSaveAiSettings}
      />
    </div>
  );
};

export default Settings;
