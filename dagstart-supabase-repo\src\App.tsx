import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import Dashboard from '@/pages/Results';
import Report from '@/pages/Report';
import Disruptions from '@/pages/Disruptions'; // Re-import Disruptions
import OverdrachtPage from '@/pages/OverdrachtPage'; // Import new page
import LogbookHistoryPage from '@/pages/LogbookHistoryPage'; // Import Logbook History page
import SafetyQuality from '@/pages/SafetyQuality';
import Materials from '@/pages/Materials';
import EquipmentCodes from '@/pages/EquipmentCodes';
import HistoryV2 from '@/pages/HistoryV2';
import Settings from '@/pages/Settings';
import Auth from '@/pages/Auth';
import TdLogbookPage from '@/pages/TdLogbookPage'; // Import TD Logbook page
import TdLogbookHistoryPage from '@/pages/TdLogbookHistoryPage'; // Import TD Logbook History page
import { ProductionProvider } from '@/context/ProductionContext';
import { AuthProvider } from '@/context/AuthContext';
import { TransactionProvider } from '@/context/TransactionContext';
import { Toaster } from '@/components/ui/toaster';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Importeer de nieuwe materialen componenten
import { MaterialManagement } from '@/components/materials/MaterialManagement';
import { MaterialComparison } from '@/components/materials/MaterialComparison';

// Configureer de toekomstige flags
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

const App: React.FC = () => {
  return (
    <Router {...router}>
      <AuthProvider>
        <TransactionProvider>
          <ProductionProvider>
            <Layout>
              <Routes>
                <Route path="/auth" element={<Auth />} />
                <Route path="/" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                {/* Removed /report route */}
                <Route path="/results" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                {/* Restored old /disruptions route */}
                 <Route path="/disruptions" element={
                  <ProtectedRoute>
                    <Disruptions />
                  </ProtectedRoute>
                } />
                {/* Updated Route for Overdracht */}
                <Route path="/overdracht" element={
                  <ProtectedRoute>
                    <OverdrachtPage />
                  </ProtectedRoute>
                } />
                <Route path="/safety-quality" element={
                  <ProtectedRoute>
                    <SafetyQuality />
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
                <Route path="/settings/targets" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
                <Route path="/settings/toggles" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
                <Route path="/settings/equipment" element={<ProtectedRoute><EquipmentCodes /></ProtectedRoute>} />
                <Route path="/settings/materials" element={<ProtectedRoute><Materials /></ProtectedRoute>}>
                  <Route index element={<MaterialManagement />} />
                  <Route path="management" element={<MaterialManagement />} />
                  <Route path="comparison" element={<MaterialComparison />} />
                </Route>
                <Route path="/history" element={
                  <ProtectedRoute>
                    <HistoryV2 />
                  </ProtectedRoute>
                } />
               {/* Logbook History Route */}
               <Route path="/logboek-historie" element={
                <ProtectedRoute>
                  <LogbookHistoryPage />
                </ProtectedRoute>
              } />
              {/* TD Logbook Route */}
              <Route path="/td-logboek" element={
                <ProtectedRoute>
                  <TdLogbookPage />
                </ProtectedRoute>
              } />
              {/* TD Logbook History Route */}
              <Route path="/td-logboek/historie" element={
                <ProtectedRoute>
                  <TdLogbookHistoryPage />
                </ProtectedRoute>
              } />
            </Routes>
            </Layout>
            <Toaster />
          </ProductionProvider>
        </TransactionProvider>
      </AuthProvider>
    </Router>
  );
};

export default App;
