import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Link } from 'react-router-dom'; // Import Link
import { useIsMobile } from '@/hooks/use-mobile';
import { CalendarDays, ClockIcon, PlusCircle, Trash2, Check, Calendar as CalendarIcon, Pencil, ChevronUp, ChevronDown, ChevronsUpDown, Check as CheckIcon, Edit, CheckSquare, ClipboardList } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Textarea as ShadcnTextarea } from '@/components/ui/textarea'; // Renamed to avoid conflict
import TextareaAutosize from 'react-textarea-autosize';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Combobox } from "@/components/ui/combobox";
import { ProductionLine, DisruptionInput, Disruption, EquipmentEntry, RcaEntry } from '@/types'; // Import RcaEntry
import LineSelector from '@/components/common/LineSelector';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format, parseISO, isValid } from 'date-fns'; // Import isValid
import { nl } from 'date-fns/locale'; // Import nl locale
import { cn } from '@/lib/utils';
import { useProduction } from '@/context/ProductionContext';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import { toast } from 'sonner';
import RcaFormDialog from '@/components/RcaFormDialog'; // Import the new dialog
import { MessageSquarePlus } from 'lucide-react'; // Import icon for new button

// Helper function to get line color classes
const getLineColorClass = (line: ProductionLine, type: 'bg' | 'text' | 'border'): string => {
  const colors = {
    tl1: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
    tl2: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
    p1: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
    p2: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200' },
    p3: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
  };
  return colors[line]?.[type] || 'bg-gray-100 text-gray-800 border-gray-200'; // Default fallback
};

// Helper function to format single dates or timestamps
const formatSingleDate = (dateString: string | undefined | null, includeTime = false): string => {
  if (!dateString) return '-';
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return 'Ongeldige datum';
    const formatString = includeTime ? 'd MMM yyyy, HH:mm' : 'd MMM yyyy';
    return format(date, formatString, { locale: nl });
  } catch (e) {
    return 'Datumfout';
  }
};


const initialFormState = {
  line: 'tl1' as ProductionLine,
  description: '',
  equipment: '',
  rootCause: '',
  actions: '',
  // oplossingen: '', // Removed from initial state
  actionOwner: '',
  lastUpdate: '',
  resolveDate: new Date().toISOString().split('T')[0],
  // gereedmelddatum removed from initial state
};

const DisruptionsPage: React.FC = () => {
  const {
    productionData,
    addDisruption,
    updateDisruption,
    removeDisruption, // Use removeDisruption from context for archiving
    equipmentOptions,
    isFlyLocked
  } = useProduction();

  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [disruptionToDelete, setDisruptionToDelete] = useState<string | null>(null);
  const isMobile = useIsMobile();
  const [editingDisruption, setEditingDisruption] = useState<Disruption | null>(null);
  const [expandedRcaId, setExpandedRcaId] = useState<string | null>(null); // State for expanded RCA section
  const [rcaDialogOpen, setRcaDialogOpen] = useState(false); // State for RCA dialog
  const [disruptionForRca, setDisruptionForRca] = useState<Disruption | null>(null); // State for disruption being analyzed
  const [rcaEntryToEdit, setRcaEntryToEdit] = useState<RcaEntry | null>(null); // State for specific RCA entry being edited
  // State for the new Resolve Dialog
  const [resolveDialogOpen, setResolveDialogOpen] = useState(false);
  const [disruptionToResolve, setDisruptionToResolve] = useState<Disruption | null>(null);
  const [resolveSolutionText, setResolveSolutionText] = useState('');

  const [formData, setFormData] = useState({...initialFormState, line: selectedLine});

  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined); // For resolveDate in 'New' form
  const [editSelectedDate, setEditSelectedDate] = useState<Date | undefined>(new Date()); // For resolveDate in 'Edit' form
  // Removed gereedmelddatum states

  const lineDisruptions = productionData[selectedLine]?.disruptions || [];

  // Prepare options for Combobox
  const currentEquipmentOptionsForCombobox = React.useMemo(() => {
    return (equipmentOptions[selectedLine] ? Object.values(equipmentOptions[selectedLine]).flat() : []).map(opt => ({ value: opt.value, label: opt.label_nl }));
  }, [selectedLine, equipmentOptions]);

  const [equipmentSearch, setEquipmentSearch] = useState('');
  const [equipmentPopoverOpen, setEquipmentPopoverOpen] = useState(false);

  useEffect(() => {
    if (dialogOpen) {
      resetForm();
      setSelectedDate(undefined); // Explicitly reset date when dialog opens
    }
  }, [dialogOpen]);

  useEffect(() => {
    setFormData(prev => ({ ...prev, line: selectedLine }));
  }, [selectedLine]);

  // HandleSubmit for NEW disruption
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const updatedFormData = {
      ...formData,
      resolveDate: selectedDate ? selectedDate.toISOString().split('T')[0] : undefined,
      // gereedmelddatum removed
    };

    const disruptionInput: DisruptionInput = {
      description: updatedFormData.description,
      equipment: updatedFormData.equipment || undefined,
      rootCause: updatedFormData.rootCause || undefined,
      actions: updatedFormData.actions || undefined,
      // oplossingen: updatedFormData.oplossingen || undefined, // Removed
      actionOwner: updatedFormData.actionOwner || undefined,
      lastUpdate: updatedFormData.lastUpdate || undefined,
      resolveDate: updatedFormData.resolveDate,
      // gereedmelddatum removed
    };

    try {
      addDisruption(selectedLine, disruptionInput);

      resetForm();
      setDialogOpen(false);
    } catch (err) {
      console.error("Failed to add disruption locally:", err);
    }
  };

  // HandleEditSubmit for EDITING disruption
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingDisruption) return;

    const updatedDisruptionData: Partial<Disruption> = {
      description: formData.description,
      equipment: formData.equipment || undefined,
      rootCause: formData.rootCause || undefined,
      actions: formData.actions || undefined,
      // oplossingen: formData.oplossingen || undefined, // Removed
      actionOwner: formData.actionOwner || undefined,
      lastUpdate: formData.lastUpdate || undefined,
      resolveDate: editSelectedDate ? editSelectedDate.toISOString().split('T')[0] : undefined,
      // gereedmelddatum removed
      // Keep existing RCA data and other fields not in the form
      rootCauseAnalysis: editingDisruption.rootCauseAnalysis,
      createdAt: editingDisruption.createdAt,
      startTime: editingDisruption.startTime,
      endTime: editingDisruption.endTime, // Keep existing endTime if any
      resolvedAt: editingDisruption.resolvedAt, // Keep existing resolvedAt if any
      pareto: editingDisruption.pareto,
      duration: editingDisruption.duration,
      gereedmelddatum: editingDisruption.gereedmelddatum // Keep existing value if any
    };

    try {
      updateDisruption(selectedLine, editingDisruption.id, updatedDisruptionData);

      resetForm();
      setEditDialogOpen(false);
      setEditingDisruption(null);
    } catch (err) {
      console.error("Failed to update disruption locally:", err);
    }
  };

  const resetForm = () => {
    setFormData({ ...initialFormState, line: selectedLine }); // Reset main form, oplossingen is already removed from initialFormState
    setSelectedDate(new Date());
    setEditSelectedDate(new Date());
    // Removed gereedmelddatum reset
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Triggers confirmation for archiving (used by red trash can)
  const handleArchiveDisruption = async (id: string) => {
    setDisruptionToDelete(id);
    setConfirmDeleteDialogOpen(true);
  };

  // Confirms the archiving action (red trash can)
  const confirmArchiveDisruption = async () => {
    if (!disruptionToDelete) return;

    try {
      // Call removeDisruption which handles archiving, pass isResolving: false
      removeDisruption(selectedLine, disruptionToDelete, false);
      // Toast is handled within removeDisruption hook function
    } catch (err) {
      console.error("Failed to archive disruption locally:", err);
      toast.error("Archiveren mislukt."); // Add error toast here
    } finally {
      setConfirmDeleteDialogOpen(false);
      setDisruptionToDelete(null);
    }
  };

  // Sets up the form for editing a disruption
  const handleEditDisruption = (disruption: Disruption) => {
    setEditingDisruption(disruption);

    setFormData({
      line: disruption.line,
      description: disruption.description,
      equipment: disruption.equipment || '',
      rootCause: disruption.rootCause || '',
      actions: disruption.actions || '',
      // oplossingen: disruption.oplossingen || '', // Removed from edit form data
      actionOwner: disruption.actionOwner || '',
      lastUpdate: disruption.lastUpdate || '',
      resolveDate: disruption.resolveDate || '',
      // gereedmelddatum removed from form data
    });

    // Set resolveDate picker
    if (disruption.resolveDate) {
      try {
         setEditSelectedDate(new Date(disruption.resolveDate));
      } catch (e) {
         setEditSelectedDate(new Date());
      }
    } else {
      setEditSelectedDate(undefined);
    }

    // Removed setting editSelectedGereedmeldDatum

    setEditDialogOpen(true);
  };

  const handleDialogOpenChange = (open: boolean) => {
    setDialogOpen(open);
    if (!open) {
      resetForm();
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      return new Date(dateString).toLocaleDateString('nl-NL', options);
    } catch (e) {
      return 'Invalid Date';
    }
  };

  const activeDisruptions = lineDisruptions.filter(d => !d.endTime);

  // Opens the Resolve Dialog (triggered by green checkmark)
  const handleOpenResolveDialog = (disruption: Disruption) => {
    setDisruptionToResolve(disruption);
    // Pre-fill the solution text from the disruption data if it exists
    setResolveSolutionText(disruption.oplossingen || '');
    setResolveDialogOpen(true);
  };

  // Handles saving the solution and resolving the disruption (triggered by "Opslaan en Afronden" in the new dialog)
  const handleConfirmResolve = async () => { // Make the function async
    if (!disruptionToResolve) return;

    try {
      // Step 1: Await the update of the disruption with the solution text
      await updateDisruption(disruptionToResolve.line, disruptionToResolve.id, {
        oplossingen: resolveSolutionText, // Save the text from the dialog state
      });
      // Consider moving the success toast until after both operations succeed,
      // but for now, let's keep it here to confirm the update part worked.
      toast.success("Oplossing opgeslagen.");

      // Step 2: Await the archiving of the disruption
      await removeDisruption(disruptionToResolve.line, disruptionToResolve.id, true);
      // Success/Error toast for removal is handled within the removeDisruption function itself

      // Step 3: Close the dialog and reset state ONLY after both succeed
      setResolveDialogOpen(false);
      setDisruptionToResolve(null);
      setResolveSolutionText('');

    } catch (err) {
      console.error("Failed to save solution or resolve disruption:", err);
      toast.error("Opslaan oplossing of oplossen mislukt.");
      // Reset state even on error to avoid inconsistent UI state
      setResolveDialogOpen(false);
      setDisruptionToResolve(null);
      setResolveSolutionText('');
    }
  };

  // Handler to open the RCA dialog
  const handleOpenRcaDialog = (disruption: Disruption, rcaEntry?: RcaEntry) => {
    setDisruptionForRca(disruption);
    setRcaEntryToEdit(rcaEntry || null); // Set the entry to edit, or null if adding new
    setRcaDialogOpen(true);
  };

  // Handler to save RCA data
  const handleSaveRca = (savedRcaEntry: RcaEntry) => { // Accept complete RcaEntry
    if (!disruptionForRca) return;

    const currentRcaArray = disruptionForRca.rootCauseAnalysis || [];
    let updatedRcaArray: RcaEntry[];

    // Check if we are editing an existing entry or adding a new one
    const existingIndex = currentRcaArray.findIndex(entry => entry.id === savedRcaEntry.id);

    if (existingIndex > -1) {
      // Update existing entry
      updatedRcaArray = [
        ...currentRcaArray.slice(0, existingIndex),
        savedRcaEntry, // Replace with the updated entry
        ...currentRcaArray.slice(existingIndex + 1),
      ];
    } else {
      // Add new entry
      updatedRcaArray = [...currentRcaArray, savedRcaEntry];
    }

    // Sort by timestamp descending after adding/editing
    updatedRcaArray.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Create the update object for the disruption
    const updatedDisruptionData: Partial<Disruption> = {
      rootCauseAnalysis: updatedRcaArray,
    };

    try {
      // Call updateDisruption with the new array
      updateDisruption(disruptionForRca.line, disruptionForRca.id, updatedDisruptionData);
      toast.success(`Root Cause Analyse ${existingIndex > -1 ? 'bijgewerkt' : 'opgeslagen'}.`);
      setDisruptionForRca(null); // Clear the disruption context
      setRcaEntryToEdit(null); // Clear editing state
    } catch (err) {
      console.error("Failed to save RCA data:", err);
      toast.error("Opslaan RCA mislukt.");
    }
  };


  return (
    <div className="animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-faerch-blue mb-4 md:mb-0">
          Lopende Storingen
        </h1>
        {/* Buttons Wrapper */}
        <div className="flex items-center gap-2">
          {/* New TD Logboek Button */}
          <Link to="/td-logboek">
            <Button variant="outline" className="flex items-center gap-2 bg-white text-black border border-gray-200 hover:bg-gray-50 rounded-md px-4 py-2">
              <ClipboardList className="w-5 h-5" />
              <span>TD logboek</span>
            </Button>
          </Link>
          {/* Existing Nieuwe Storing Button */}
          <Dialog open={dialogOpen} onOpenChange={handleDialogOpenChange}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2 bg-white text-black border border-gray-200 hover:bg-gray-50 rounded-md px-4 py-2">
                <PlusCircle className="w-5 h-5" />
                <span>Nieuwe Storing</span>
              </Button>
            </DialogTrigger>
          {/* Removed "Extra Mededeling" button */}
          <DialogContent className="md:max-w-2xl">
            <DialogHeader>
              <DialogTitle>Nieuwe storing toevoegen</DialogTitle>
              <DialogDescription>
                Vul de gegevens in om een nieuwe storing te registreren.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4 mt-4">
              {/* Grid for Line and Equipment */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="disruption-line">Productielijn</Label>
                  <Select
                    value={formData.line}
                    onValueChange={(value) => handleInputChange('line', value as ProductionLine)}
                  >
                    <SelectTrigger id="disruption-line">
                      <SelectValue placeholder="Selecteer productielijn" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tl1">TL1</SelectItem>
                      <SelectItem value="tl2">TL2</SelectItem>
                      <SelectItem value="p1">P1</SelectItem>
                      <SelectItem value="p2">P2</SelectItem>
                      <SelectItem value="p3">P3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="disruption-equipment">Onderdeel</Label>
                  <Popover open={equipmentPopoverOpen} onOpenChange={setEquipmentPopoverOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={equipmentPopoverOpen}
                        className="w-full justify-between font-normal h-10 px-5 py-2 text-sm btn-text-gradient hover:scale-105 mt-1"
                      >
                        {/* Display the actual value from formData, whether selected or typed */}
                        {formData.equipment || "Selecteer of typ onderdeel..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                      <Command filter={(value, search) => {
                          const item = currentEquipmentOptionsForCombobox.find(opt => opt.value === value);
                          if (!item) return 0;
                          const searchTerm = search.toLowerCase();
                          const labelMatch = item.label.toLowerCase().includes(searchTerm);
                          const valueMatch = item.value.toLowerCase().includes(searchTerm);
                          return labelMatch || valueMatch ? 1 : 0;
                      }}>
                        <CommandInput
                          placeholder="Zoek onderdeel (naam of code)..."
                          value={equipmentSearch}
                          onValueChange={setEquipmentSearch}
                        />
                        <CommandList>
                          <CommandEmpty>Geen onderdeel gevonden.</CommandEmpty>
                          <CommandGroup>
                            {/* Add custom option if search doesn't match existing */}
                            {equipmentSearch && !currentEquipmentOptionsForCombobox.some(opt => opt.label.toLowerCase() === equipmentSearch.toLowerCase() || opt.value.toLowerCase() === equipmentSearch.toLowerCase()) && (
                              <CommandItem
                                key="custom-value"
                                value={equipmentSearch} // Use search term as value
                                onSelect={(currentValue) => {
                                  handleInputChange('equipment', currentValue); // Save the typed value
                                  setEquipmentPopoverOpen(false);
                                  setEquipmentSearch('');
                                }}
                                className="text-muted-foreground italic"
                              >
                                Gebruik: "{equipmentSearch}"
                              </CommandItem>
                            )}
                            {/* Map existing options */}
                            {currentEquipmentOptionsForCombobox.map((option) => (
                              <CommandItem
                                key={option.value}
                                value={option.value} // Use the actual value for selection
                                onSelect={(currentValue) => {
                                  // Find the selected option object to get its value
                                  const selectedOption = currentEquipmentOptionsForCombobox.find(opt => opt.value === currentValue);
                                  const valueToSet = selectedOption ? selectedOption.value : ''; // Ensure we save the value
                                  handleInputChange('equipment', valueToSet === formData.equipment ? "" : valueToSet);
                                  setEquipmentPopoverOpen(false);
                                  setEquipmentSearch('');
                                }}
                              >
                                <CheckIcon
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    // Check against the actual value in formData
                                    formData.equipment === option.value ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {option.label} ({option.value})
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Single column fields */}
              <div className="space-y-2">
                <Label htmlFor="disruption-description">Wat is het probleem?</Label>
                {/* Using ShadcnTextarea with user-specified classes and style */}
                <ShadcnTextarea
                  id="disruption-description"
                  placeholder="Beschrijf het probleem"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  required
                  className="w-full text-sm p-2 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2" // User-specified classes
                  style={{ height: '47.2222px !important' }} // User-specified inline style
                />
              </div>

              {/* Swap order: Acties first */}
              <div className="space-y-2">
                <Label htmlFor="disruption-actions">Acties</Label>
                {/* Using ShadcnTextarea here as autosize wasn't requested for this one */}
                <ShadcnTextarea
                  id="disruption-actions"
                  placeholder="Beschrijf de acties"
                  value={formData.actions}
                  onChange={(e) => handleInputChange('actions', e.target.value)}
                  rows={2}
                />
              </div>
{/* Oplossing Textarea removed from New Disruption Dialog */}
{/* End swap */}

              <div className="space-y-2">
                <Label htmlFor="disruption-owner">Actiehouder</Label>
                <Input
                  id="disruption-owner"
                  placeholder="Naam van de actiehouder"
                  value={formData.actionOwner}
                  onChange={(e) => handleInputChange('actionOwner', e.target.value)}
                />
              </div>

              {/* Grid for bottom row fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {/* Adjusted grid cols */}
                <div className="space-y-2">
                  <Label htmlFor="disruption-last-update-by">Laatste update door</Label>
                  <Input id="disruption-last-update-by" placeholder="Naam" value={formData.lastUpdate} onChange={(e) => handleInputChange('lastUpdate', e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="disruption-resolve-date">Leverdatum</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                       <Button id="disruption-resolve-date" variant={"outline"} className={cn("w-full justify-start text-left font-normal", !selectedDate && "text-muted-foreground")}><CalendarIcon className="mr-2 h-4 w-4" />{selectedDate ? format(selectedDate, "dd-MM-yyyy") : <span>Selecteer een datum</span>}</Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start"><Calendar mode="single" selected={selectedDate} onSelect={setSelectedDate} defaultMonth={selectedDate} /></PopoverContent>
                  </Popover>
                </div>
                {/* Removed Gereedmelddatum Picker */}
              </div>

              {/* Submit Buttons */}
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                  Annuleren
                </Button>
                <Button type="submit" className="bg-faerch-blue hover:bg-faerch-teal">
                  Storing opslaan
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
        </div> {/* End Buttons Wrapper */}
      </div>

      {/* ==================== EDIT DIALOG ==================== */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="md:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Storing bewerken</DialogTitle>
            <DialogDescription>
              Wijzig de gegevens van de storing.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit} className="space-y-4 mt-4">
            {/* Grid for Line and Equipment */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Productielijn</Label>
                <Input value={formData.line.toUpperCase()} readOnly disabled className="bg-gray-100" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-disruption-equipment">Onderdeel</Label>
                <Popover open={equipmentPopoverOpen} onOpenChange={setEquipmentPopoverOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={equipmentPopoverOpen}
                      className="w-full justify-between font-normal h-10 px-5 py-2 text-sm btn-text-gradient hover:scale-105 mt-1"
                    >
                      {/* Display the actual value from formData, whether selected or typed */}
                      {formData.equipment || "Selecteer of typ onderdeel..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                    <Command filter={(value, search) => {
                        const item = currentEquipmentOptionsForCombobox.find(opt => opt.value === value);
                        if (!item) return 0;
                        const searchTerm = search.toLowerCase();
                        const labelMatch = item.label.toLowerCase().includes(searchTerm);
                        const valueMatch = item.value.toLowerCase().includes(searchTerm);
                        return labelMatch || valueMatch ? 1 : 0;
                    }}>
                      <CommandInput
                        placeholder="Zoek onderdeel (naam of code)..."
                        value={equipmentSearch}
                        onValueChange={setEquipmentSearch}
                      />
                      <CommandList>
                        <CommandEmpty>Geen onderdeel gevonden.</CommandEmpty>
                        <CommandGroup>
                          {/* Add custom option if search doesn't match existing */}
                          {equipmentSearch && !currentEquipmentOptionsForCombobox.some(opt => opt.label.toLowerCase() === equipmentSearch.toLowerCase() || opt.value.toLowerCase() === equipmentSearch.toLowerCase()) && (
                            <CommandItem
                              key="custom-value"
                              value={equipmentSearch} // Use search term as value
                              onSelect={(currentValue) => {
                                handleInputChange('equipment', currentValue); // Save the typed value
                                setEquipmentPopoverOpen(false);
                                setEquipmentSearch('');
                              }}
                              className="text-muted-foreground italic"
                            >
                              Gebruik: "{equipmentSearch}"
                            </CommandItem>
                          )}
                          {/* Map existing options */}
                          {currentEquipmentOptionsForCombobox.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.value} // Use the actual value for selection
                              onSelect={(currentValue) => {
                                // Find the selected option object to get its value
                                const selectedOption = currentEquipmentOptionsForCombobox.find(opt => opt.value === currentValue);
                                const valueToSet = selectedOption ? selectedOption.value : ''; // Ensure we save the value
                                handleInputChange('equipment', valueToSet === formData.equipment ? "" : valueToSet);
                                setEquipmentPopoverOpen(false);
                                setEquipmentSearch('');
                              }}
                            >
                              <CheckIcon
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  // Check against the actual value in formData
                                  formData.equipment === option.value ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {option.label} ({option.value})
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Single column fields */}
            <div className="space-y-2">
              <Label htmlFor="edit-disruption-description">Wat is het probleem?</Label>
              {/* Using ShadcnTextarea with user-specified classes and style */}
              <ShadcnTextarea
                id="edit-disruption-description"
                placeholder="Beschrijf het probleem"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                required
                className="w-full text-sm p-2 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2" // User-specified classes
                style={{ height: '47.2222px !important' }} // User-specified inline style
            />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-disruption-actions">Acties</Label>
              {/* Using ShadcnTextarea here as autosize wasn't requested for this one */}
              <ShadcnTextarea
                id="edit-disruption-actions"
                placeholder="Beschrijf de acties"
                value={formData.actions}
                onChange={(e) => handleInputChange('actions', e.target.value)}
                rows={2}
              />
            </div>
            {/* Oplossing Textarea removed from Edit Disruption Dialog */}


            <div className="space-y-2">
              <Label htmlFor="edit-disruption-owner">Actiehouder</Label>
              <Input
                id="edit-disruption-owner"
                placeholder="Naam van de actiehouder"
                value={formData.actionOwner}
                onChange={(e) => handleInputChange('actionOwner', e.target.value)}
              />
            </div>

            {/* Grid for bottom row fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {/* Adjusted grid cols */}
              <div className="space-y-2">
                <Label htmlFor="edit-disruption-last-update-by">Laatste update door</Label>
                <Input id="edit-disruption-last-update-by" placeholder="Naam" value={formData.lastUpdate} onChange={(e) => handleInputChange('lastUpdate', e.target.value)} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-disruption-resolve-date">Leverdatum</Label>
                 <Popover>
                   <PopoverTrigger asChild>
                      <Button id="edit-disruption-resolve-date" variant={"outline"} className={cn("w-full justify-start text-left font-normal", !editSelectedDate && "text-muted-foreground")}><CalendarIcon className="mr-2 h-4 w-4" />{editSelectedDate ? format(editSelectedDate, "dd-MM-yyyy") : <span>Selecteer een datum</span>}</Button>
                   </PopoverTrigger>
                   <PopoverContent className="w-auto p-0" align="start"><Calendar mode="single" selected={editSelectedDate} onSelect={setEditSelectedDate} initialFocus /></PopoverContent>
                 </Popover>
               </div>
               {/* Removed Gereedmelddatum Picker */}
            </div>

            {/* Submit Buttons */}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setEditDialogOpen(false)}>
                Annuleren
              </Button>
              <Button type="submit" className="bg-faerch-blue hover:bg-faerch-teal">
                Wijzigingen opslaan
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* ==================== DELETE CONFIRM DIALOG ==================== */}
      <Dialog open={confirmDeleteDialogOpen} onOpenChange={setConfirmDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Archiveren bevestigen</DialogTitle> {/* Changed title */}
            <DialogDescription>
              Weet je zeker dat je deze storing wilt archiveren? Deze actie kan niet ongedaan worden gemaakt.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setConfirmDeleteDialogOpen(false);
                setDisruptionToDelete(null);
              }}
            >
              Annuleren
            </Button>
            <Button
              variant="destructive"
              onClick={confirmArchiveDisruption} // Use renamed handler
            >
              Archiveren
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Line Selector */}
      <div className="mb-6">
        <LineSelector selectedLine={selectedLine} onChange={setSelectedLine} />
      </div>

      {/* Disruptions List */}
      <div className="dashboard-card p-5">
        <h2 className="text-xl font-semibold mb-4">Actieve storingen voor {selectedLine.toUpperCase()}</h2>
        {activeDisruptions.length === 0 ? (
          <p className="text-center text-gray-500 italic py-8">Geen actieve storingen voor {selectedLine.toUpperCase()}.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activeDisruptions.map(disruption => { // Start block for map item
              // Add null check for disruption.id
              if (!disruption?.id) {
                console.warn("Rendering disruption without ID:", disruption);
                return null; // Skip rendering if ID is missing
              }
              const isRcaExpanded = expandedRcaId === disruption.id; // Define isRcaExpanded here
              return ( // Return the JSX for each item
              <div key={disruption.id} className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden">
                {/* Header */}
                <div className={`px-4 py-3 flex justify-between items-center border-b ${getLineColorClass(disruption.line, 'border')}`}>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-block px-2 py-1 text-xs ${getLineColorClass(disruption.line, 'bg')} ${getLineColorClass(disruption.line, 'text')} rounded uppercase font-medium`}>{disruption.line}</span>
                    <h3 className="font-medium text-sm">{disruption.createdAt ? formatDate(disruption.createdAt) : 'N/A'}</h3>
                  </div>
                  <div className="flex space-x-1">
                     {/* RCA Toggle Button */}
                     <Button
                       variant="ghost"
                       size="icon"
                       className="h-7 w-7 text-purple-500 hover:text-purple-700 hover:bg-purple-50"
                       onClick={() => setExpandedRcaId(isRcaExpanded ? null : disruption.id)}
                       aria-label="Root Cause Analyse"
                     >
                       {isRcaExpanded ? <ChevronUp className="h-4 w-4" /> : <ClipboardList className="h-4 w-4" />}
                     </Button>
                     {/* Edit Button */}
                     <Button
                       variant="ghost"
                       size="icon"
                       className="h-7 w-7 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                       onClick={() => handleEditDisruption(disruption)}
                       aria-label="Bewerken"
                     >
                       <Pencil className="h-4 w-4" />
                     </Button>
                     {/* Resolve Button */}
                     <Button
                       variant="ghost"
                       size="icon"
                       className="h-7 w-7 text-green-500 hover:text-green-700 hover:bg-green-50"
                       onClick={() => handleOpenResolveDialog(disruption)} // Correct: This now opens the new dialog
                       aria-label="Oplossen"
                     >
                       <CheckSquare className="h-4 w-4" />
                     </Button>
                     {/* Archive Button (Trash Can) */}
                     <Button
                       variant="ghost"
                       size="icon"
                       className="h-7 w-7 text-red-500 hover:text-red-700 hover:bg-red-50"
                       onClick={() => handleArchiveDisruption(disruption.id)} // Use handleArchiveDisruption
                       aria-label="Archiveren" // Changed label
                       disabled={isFlyLocked} // Disable if fly is locked
                     >
                       <Trash2 className="h-4 w-4" />
                     </Button>
                   </div>
                 </div>
                {/* Equipment */}
                {disruption.equipment && (
                  <div className="bg-amber-50 px-4 pb-2 text-sm border-b border-amber-100">
                    <span className="text-gray-600">Onderdeel:</span>{' '}
                    <span className="font-medium text-gray-800">{disruption.equipment}</span>
                  </div>
                )}
                {/* Main Content */}
                <div className="p-4 space-y-3">
                   <div>
                    <h4 className="text-gray-500 text-xs font-medium uppercase tracking-wider mb-1">Probleem</h4>
                    {/* Replace p tag with TextareaAutosize for display */}
                    {/* Reverted back to p tag for display in list view */}
                    <p className="text-sm">{disruption.description}</p>
                  </div>

                  {disruption.rootCause && (
                    <div>
                      <h4 className="text-gray-500 text-xs font-medium uppercase tracking-wider mb-1">Kernoorzaak</h4>
                      <p className="text-sm">{disruption.rootCause}</p>
                    </div>
                  )}

                  {disruption.actions && (
                    <div>
                      <h4 className="text-gray-500 text-xs font-medium uppercase tracking-wider mb-1">Acties</h4>
                      <p className="text-sm">{disruption.actions}</p>
                    </div>
                  )}

                  {disruption.oplossingen && (
                    <div>
                      <h4 className="text-gray-500 text-xs font-medium uppercase tracking-wider mb-1">Oplossing</h4>
                      <p className="text-sm">{disruption.oplossingen}</p>
                    </div>
                  )}

                  <div className="pt-2 border-t border-gray-100 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                    {disruption.actionOwner && (
                      <div>
                        <p className="text-gray-500 text-xs">Actiehouder:</p>
                        <p className="font-medium">{disruption.actionOwner}</p>
                      </div>
                    )}

                    {disruption.gereedmelddatum && (
                      <div>
                        <p className="text-gray-500 text-xs">Gereed gemeld:</p>
                        <p className="font-medium">{formatSingleDate(disruption.gereedmelddatum)}</p> {/* Use helper */}
                      </div>
                    )}

                    {disruption.resolveDate && (
                      <div>
                        <p className="text-gray-500 text-xs">Lever datum:</p>
                        <p className="font-medium">{formatSingleDate(disruption.resolveDate)}</p> {/* Use helper */}
                      </div>
                    )}

                    {disruption.lastUpdate && (
                      <div>
                        <p className="text-gray-500 text-xs">Update door:</p>
                        <p className="font-medium">{disruption.lastUpdate}</p>
                      </div>
                    )}
                   </div>
                 </div>
                 {/* RCA Section - Updated */}
                 {isRcaExpanded && (
                   <div className="p-4 border-t border-gray-100 bg-gray-50 space-y-3">
                     <div className="flex justify-between items-center mb-2">
                       <h4 className="text-sm font-semibold text-gray-700">Root Cause Analyse</h4>
                       {/* Pass disruption without specific entry to add new */}
                       <Button variant="link" size="sm" className="text-xs h-auto p-0" onClick={() => handleOpenRcaDialog(disruption)}>
                         Nieuwe Analyse Toevoegen
                       </Button>
                     </div>
                     {/* Map over the RCA entries array */}
                     {disruption.rootCauseAnalysis && disruption.rootCauseAnalysis.length > 0 ? (
                       disruption.rootCauseAnalysis.map((rcaEntry) => { // Use rcaEntry.id as key
                         // Add null check for rcaEntry.id
                         if (!rcaEntry?.id) {
                            console.warn("Rendering RCA entry without ID:", rcaEntry);
                            return null; // Skip rendering if ID is missing
                         }
                         return (
                         <div key={rcaEntry.id} className="p-3 border rounded bg-white mb-2 shadow-sm relative group"> {/* Added relative group */}
                           {/* Edit button */}
                           <Button
                             variant="ghost"
                             size="icon"
                             className="absolute top-1 right-1 h-6 w-6 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity"
                             onClick={() => handleOpenRcaDialog(disruption, rcaEntry)} // Pass entry to edit
                             aria-label="Bewerk Analyse"
                           >
                             <Pencil className="h-3.5 w-3.5" />
                           </Button>
                           <p className="text-xs text-gray-500 mb-2 font-medium">Analyse van: {formatSingleDate(rcaEntry.timestamp, true)}</p>
                           <div className="space-y-1 text-xs">
                             {rcaEntry.who && <p><span className="font-medium">Wie:</span> {rcaEntry.who}</p>}
                             {rcaEntry.what && <p><span className="font-medium">Wat:</span> {rcaEntry.what}</p>}
                             {rcaEntry.where && <p><span className="font-medium">Waar:</span> {rcaEntry.where}</p>}
                             {rcaEntry.how && <p><span className="font-medium">Hoe:</span> {rcaEntry.how}</p>}
                             {rcaEntry.followUp && <p><span className="font-medium">Vervolg:</span> {rcaEntry.followUp}</p>}
                             {/* Removed RCA Solution display */}
                           </div>
                         </div>
                       )})
                     ) : (
                       <p className="text-xs text-gray-500 italic">Nog geen analyses toegevoegd.</p>
                     )}
                   </div>
                 )}
               </div>
             )})} {/* Close the return and map function block */}
          </div>
        )}
      </div>

      {/* Render RCA Dialog */}
      <RcaFormDialog
        disruption={disruptionForRca}
        rcaEntryToEdit={rcaEntryToEdit} // Pass the entry to edit
        open={rcaDialogOpen}
        onOpenChange={setRcaDialogOpen}
        onSave={handleSaveRca}
      />

      {/* Resolve Disruption Dialog (New) */}
      <Dialog open={resolveDialogOpen} onOpenChange={setResolveDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Storing Oplossen</DialogTitle>
            <DialogDescription>
              Voer de oplossing in voordat u de storing afrondt. Deze wordt opgeslagen bij de storing.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="resolve-solution">Oplossing</Label>
              {/* Using ShadcnTextarea here as autosize wasn't requested for this one */}
              <ShadcnTextarea
                id="resolve-solution"
                placeholder="Beschrijf de gevonden oplossing"
                value={resolveSolutionText}
                onChange={(e) => setResolveSolutionText(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setResolveDialogOpen(false)}>Annuleren</Button>
            {/* This button now saves the solution AND resolves/archives */}
            <Button onClick={handleConfirmResolve} disabled={!disruptionToResolve}>Opslaan en Afronden</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
};

export default DisruptionsPage;
