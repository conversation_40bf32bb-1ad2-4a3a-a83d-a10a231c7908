
import { ProductionLine, ProductionRow } from '@/types';
import { SHIFT_TARGETS, YIELD_TARGETS } from '@/config/targets';

// Calculate target per shift (total target / 3)
export const getShiftTarget = (line: ProductionLine): number => {
  return SHIFT_TARGETS[line];
};

// Calculate total production for a row
export const getTotalProduction = (row: ProductionRow): number => {
  // Access production values from shift data objects
  const odProduction = Number(row.od?.production || 0);
  const mdProduction = Number(row.md?.production || 0);
  const ndProduction = Number(row.nd?.production || 0);
  
  return odProduction + mdProduction + ndProduction;
};

// Calculate efficiency (total production / target) * 100
export const getEfficiency = (row: ProductionRow): number => {
  return (getTotalProduction(row) / row.target) * 100;
};

// Calculate efficiency percentage for a specific value
export const calculateEfficiencyPercent = (value: number, target: number): number => {
  if (target === 0) return 0;
  return parseFloat(((value / target) * 100).toFixed(1));
};

// Check if a value meets or exceeds the target
export const isTargetReached = (value: number, target: number): boolean => {
  return value >= target;
};

// Calculate average of non-zero values
export const calculateAverage = (values: number[]): number => {
  const nonZeroValues = values.filter(v => v > 0);
  if (nonZeroValues.length === 0) return 0;
  
  const sum = nonZeroValues.reduce((acc, val) => acc + val, 0);
  return sum / nonZeroValues.length;
};

// Format date string to readable format (e.g., "10 juni 2023")
export const formatDate = (dateStr: string): string => {
  if (!dateStr) return '';
  
  const date = new Date(dateStr);
  return date.toLocaleDateString('nl-NL', { 
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};

// Format number with thousand separator
export const formatNumber = (num: number): string => {
  return num.toLocaleString('nl-NL');
};

// Format percentage
export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1).replace('.', ',')}%`;
};

// Get color for production line
export const getLineColor = (line: ProductionLine): string => {
  const colors: Record<ProductionLine, string> = {
    'tl1': '#4F46E5', // Indigo
    'tl2': '#2563EB', // Blue
    'p1': '#7C3AED',  // Violet
    'p2': '#9333EA',  // Purple
    'p3': '#C026D3',  // Fuchsia
  };
  return colors[line];
};

// Get color for target line
export const getLineTargetColor = (line: ProductionLine): string => {
  const colors: Record<ProductionLine, string> = {
    'tl1': '#818CF8', // Lighter Indigo
    'tl2': '#93C5FD', // Lighter Blue
    'p1': '#A78BFA', // Lighter Violet
    'p2': '#C4B5FD', // Lighter Purple
    'p3': '#E879F9', // Lighter Fuchsia
  };
  return colors[line];
};

// Calculating the maximum value for y-axis scale to ensure target line isn't at the top
export const calculateChartMaximum = (productionData: number[], targetValue: number): number => {
  const maxProduction = Math.max(...productionData, 0);
  const maxValue = Math.max(maxProduction, targetValue);
  
  // Make the max at least 50% larger than the target to ensure target line isn't at top
  return Math.max(maxValue * 1.2, targetValue * 1.5);
};
