# Cirrec Dagstart

This project is a web application designed as a production dashboard for Cirrec.

## Developed By

Cirrec Netherlands B.V.
Roelofshoeveweg 43
6921 RH Duiven
The Netherlands
+31 851 052 160
[https://www.cirrec.nl](https://www.cirrec.nl)

## Tech Stack

This project is built using the following technologies:

*   **Language:** [TypeScript](https://www.typescriptlang.org/docs/) - A typed superset of JavaScript.
*   **Framework:** [React](https://react.dev/) - A JavaScript library for building user interfaces.
*   **Build Tool:** [Vite](https://vitejs.dev/guide/) - A fast frontend build tool.
*   **Routing:** [React Router](https://reactrouter.com/en/main) - Handles client-side navigation within the single-page application.
*   **Styling:** [Tailwind CSS](https://tailwindcss.com/docs/installation) - A utility-first CSS framework.
*   **UI Components:** [shadcn/ui](https://ui.shadcn.com/docs) - Re-usable components built using Radix UI and Tailwind CSS. Configuration is in `components.json`.
*   **State Management:** [React Context API](https://react.dev/learn/passing-data-deeply-with-context) - Used for managing global state (see `src/context/` and `src/hooks/use-production-state.ts`).
*   **Backend & Real-time:** [Supabase](https://supabase.com/docs) - Currently used as the backend-as-a-service for data storage (via `src/lib/supabase-storage.ts` interacting with the `local_storage_data` table) and real-time data synchronization (via Supabase Realtime subscriptions configured in `src/hooks/use-production-state.ts`).
*   **Date Handling:** [date-fns](https://date-fns.org/docs/Getting-Started) - For date formatting and manipulation.
*   **Icons:** [Lucide React](https://lucide.dev/guide/packages/lucide-react) - Icon library.
*   **Notifications:** [Sonner](https://sonner.emilkowal.ski/introduction) - Toast notifications.

## Running Locally

1.  Install dependencies:
    ```bash
    npm install
    ```
2.  Start the development server:
    ```bash
    npm run dev
    ```
    *(Note: Ensure you have appropriate Supabase environment variables set up in a `.env` file - see Environment Variables section below).*

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

You can find these values in your Supabase project dashboard under Project Settings > API.

## Supabase Database Setup

This application requires a Supabase project with specific tables, views, policies, and storage buckets. Follow these steps to set up your database for migration.

### Database Tables

#### 1. local_storage_data

```sql
CREATE TABLE public.local_storage_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### 2. td_logbook_entries

```sql
CREATE TABLE public.td_logbook_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    text TEXT NOT NULL,
    location TEXT NOT NULL,
    priority TEXT NOT NULL,
    team TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID REFERENCES auth.users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### 3. td_reporter_names

```sql
CREATE TABLE public.td_reporter_names (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    number TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### 4. authorized_managers

```sql
CREATE TABLE public.authorized_managers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### 5. user_profiles

```sql
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    display_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### 6. user_teams

```sql
CREATE TABLE public.user_teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    team TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### 7. todos

```sql
CREATE TABLE public.todos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    assigned_teams TEXT[] NOT NULL,
    priority TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'open',
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    completed_at TIMESTAMP WITH TIME ZONE,
    completed_by UUID REFERENCES auth.users(id),
    due_date TIMESTAMP WITH TIME ZONE,
    comments JSONB[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}'
);
```

#### 8. notifications

```sql
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    type TEXT NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    reference_id UUID,
    reference_type TEXT
);
```

### Database Views

#### 1. auth_users_view

```sql
CREATE VIEW public.auth_users_view AS
SELECT
    users.id,
    users.email,
    users.created_at,
    users.last_sign_in_at,
    users.email_confirmed_at
FROM auth.users;
```

### Row Level Security (RLS) Policies

#### 1. local_storage_data

```sql
ALTER TABLE public.local_storage_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable all access for authenticated users" ON public.local_storage_data FOR ALL TO authenticated USING (true) WITH CHECK (true);
CREATE POLICY "Enable read access for anonymous users" ON public.local_storage_data FOR SELECT TO anon USING (true);
```

#### 2. td_logbook_entries

```sql
ALTER TABLE public.td_logbook_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated insert access" ON public.td_logbook_entries FOR INSERT TO authenticated WITH CHECK (true);
CREATE POLICY "Allow authenticated read access" ON public.td_logbook_entries FOR SELECT TO anon, authenticated USING (auth.role() = 'authenticated');
```

#### 3. td_reporter_names

```sql
ALTER TABLE public.td_reporter_names ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated read access" ON public.td_reporter_names FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated insert access" ON public.td_reporter_names FOR INSERT TO authenticated WITH CHECK (true);
CREATE POLICY "Allow authenticated delete access" ON public.td_reporter_names FOR DELETE TO authenticated USING (true);
```

#### 4. authorized_managers

```sql
ALTER TABLE public.authorized_managers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated users to read their own entry" ON public.authorized_managers FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Allow service_role to manage entries" ON public.authorized_managers USING (true) WITH CHECK (true);
```

#### 5. user_profiles

```sql
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile" ON public.user_profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own profile" ON public.user_profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own profile" ON public.user_profiles FOR INSERT WITH CHECK (auth.uid() = user_id);
```

#### 6. user_teams

```sql
ALTER TABLE public.user_teams ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own team" ON public.user_teams FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Managers can view all teams" ON public.user_teams FOR SELECT USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
CREATE POLICY "Managers can insert teams" ON public.user_teams FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
CREATE POLICY "Managers can update teams" ON public.user_teams FOR UPDATE USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
```

#### 7. todos

```sql
ALTER TABLE public.todos ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow managers to view all todos" ON public.todos FOR SELECT USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
CREATE POLICY "Allow users to view todos for their team" ON public.todos FOR SELECT USING (EXISTS (SELECT 1 FROM user_teams WHERE user_teams.user_id = auth.uid() AND user_teams.team = ANY(todos.assigned_teams)));
CREATE POLICY "Allow managers to insert todos" ON public.todos FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
CREATE POLICY "Allow managers to update any todo" ON public.todos FOR UPDATE USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
CREATE POLICY "Allow team members to update completion status" ON public.todos FOR UPDATE USING (EXISTS (SELECT 1 FROM user_teams WHERE user_teams.user_id = auth.uid() AND user_teams.team = ANY(todos.assigned_teams)));
CREATE POLICY "Allow managers to delete any todo" ON public.todos FOR DELETE USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));
```

### Storage Buckets

```sql
-- Create a private storage bucket for images with 3MB file size limit
INSERT INTO storage.buckets (id, name, public, file_size_limit)
VALUES ('images', 'images', false, 3145728);

-- Set up RLS for the images bucket
CREATE POLICY "Authenticated users can upload images"
ON storage.objects FOR INSERT
TO authenticated WITH CHECK (bucket_id = 'images');

CREATE POLICY "Authenticated users can view images"
ON storage.objects FOR SELECT
TO authenticated USING (bucket_id = 'images');
```

### Initial Data Setup

```sql
-- Add default managers
INSERT INTO public.authorized_managers (user_id)
SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Add team assignments
INSERT INTO public.user_teams (user_id, team)
SELECT id, 'Blauw' FROM auth.users WHERE email = '<EMAIL>';

INSERT INTO public.user_teams (user_id, team)
SELECT id, 'Geel' FROM auth.users WHERE email = '<EMAIL>';
```

### Authentication Setup

1. In your Supabase project, go to Authentication > Settings
2. Disable "Enable email confirmations" if your company firewall blocks confirmation emails
3. Set up a custom email template for password recovery if needed
4. Configure the Site URL to match your application's URL

## Backend Migration (from Supabase to Custom SQL Database)

The current implementation relies heavily on Supabase for data persistence and real-time updates. To migrate to a custom backend with an SQL database:

1.  **Create a Backend API:**
    *   Build a separate backend application (e.g., using Node.js/Express, Python/Flask/Django, C#/ASP.NET Core, etc.).
    *   This API needs to connect to your SQL database.
    *   Implement API endpoints (e.g., REST or GraphQL) to perform CRUD (Create, Read, Update, Delete) operations on your data (production rows, disruptions, history, settings, etc.). These endpoints will replace the direct Supabase calls.

2.  **Modify Frontend Data Logic:**
    *   Replace the Supabase client setup (`src/lib/supabase-client.ts`).
    *   Rewrite the data interaction logic in `src/lib/supabase-storage.ts` (or create a new API client module) to call your new backend API endpoints instead of Supabase functions (`supabase.from(...).select()`, `.insert()`, `.update()`, `.delete()`).
    *   Update the state management hook (`src/hooks/use-production-state.ts`) to use these new API-calling functions.

3.  **Implement Real-time Updates (Optional but Recommended):**
    *   Supabase Realtime provides live updates. To replicate this, your custom backend API needs a real-time communication mechanism (e.g., WebSockets using libraries like Socket.IO, or Server-Sent Events).
    *   The backend should push updates to connected frontend clients when data changes in the SQL database.
    *   The frontend (`src/hooks/use-production-state.ts`) needs to be updated to connect to this new real-time service and handle incoming update messages to keep the UI synchronized across different users.

## Hosting Configuration (SPA Routing)

This is a Single Page Application (SPA). When hosting on a server other than Netlify, you need to configure the webserver to handle client-side routing correctly.

*   **Problem:** When a user directly accesses a path like `/history` or refreshes the page on such a path, the server might look for a file named `history` instead of serving the main application entry point (`index.html`). This causes a 404 "Not Found" error.
*   **Solution:** Configure your web server (e.g., Apache, Nginx, IIS, Caddy, or a custom Node.js server) to redirect all requests for non-existent files/paths to your root `index.html` file. This allows the React Router library within your JavaScript bundle to handle the routing on the client-side.
*   **Example Concept (Specific rules vary by server):**
    *   For Netlify, we used `netlify.toml` with `[[redirects]] from = "/*" to = "/index.html" status = 200`.
    *   For Apache, you might use `.htaccess` with `mod_rewrite` rules.
    *   For Nginx, you might use `try_files $uri $uri/ /index.html;` within a `location` block.
    *   Consult the documentation for your specific web server software on how to implement SPA fallback or URL rewrite rules.
