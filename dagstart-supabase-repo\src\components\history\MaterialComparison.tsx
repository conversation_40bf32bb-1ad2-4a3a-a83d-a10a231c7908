
import React, { useState, useEffect } from 'react';
import { useProduction } from '@/context/ProductionContext';
import LineSelector from '@/components/common/LineSelector';
import { ProductionLine } from '@/types';
import {
  Composed<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Bar,
} from 'recharts';

interface MaterialComparisonProps {
  selectedLine: ProductionLine;
  setSelectedLine: (line: ProductionLine) => void;
}

const MaterialComparison: React.FC<MaterialComparisonProps> = ({ selectedLine, setSelectedLine }) => {
  const { productionData, YIELD_TARGETS } = useProduction();
  const [material1, setMaterial1] = useState<string>('');
  const [material2, setMaterial2] = useState<string>('');
  const [data, setData] = useState<any[]>([]);

  useEffect(() => {
    if (productionData && productionData[selectedLine]) {
      const lineData = productionData[selectedLine];
      const materialOptions = Array.from(new Set(lineData.rows.flatMap(row => [row.od.material, row.md.material, row.nd.material]).filter(Boolean))) as string[];

      if (materialOptions.length >= 2) {
        setMaterial1(materialOptions[0]);
        setMaterial2(materialOptions[1]);
      } else if (materialOptions.length === 1) {
        setMaterial1(materialOptions[0]);
        setMaterial2('');
      } else {
        setMaterial1('');
        setMaterial2('');
      }
    }
  }, [selectedLine, productionData]);

  useEffect(() => {
    if (productionData && productionData[selectedLine]) {
      const lineData = productionData[selectedLine];

      const filteredData = lineData.rows.flatMap(row => {
        const odProduction = row.od.material === material1 ? Number(row.od.production) : 0;
        const mdProduction = row.md.material === material1 ? Number(row.md.production) : 0;
        const ndProduction = row.nd.material === material1 ? Number(row.nd.production) : 0;
        const totalProduction1 = odProduction + mdProduction + ndProduction;

        const odProduction2 = row.od.material === material2 ? Number(row.od.production) : 0;
        const mdProduction2 = row.md.material === material2 ? Number(row.md.production) : 0;
        const ndProduction2 = row.nd.material === material2 ? Number(row.nd.production) : 0;
        const totalProduction2 = odProduction2 + mdProduction2 + ndProduction2;

        return {
          date: row.date,
          [material1 || 'Materiaal 1']: totalProduction1,
          [material2 || 'Materiaal 2']: totalProduction2,
        };
      });

      setData(filteredData);
    }
  }, [selectedLine, productionData, material1, material2]);

  const materialOptions = productionData && productionData[selectedLine]
    ? Array.from(new Set(productionData[selectedLine].rows.flatMap(row => [row.od.material, row.md.material, row.nd.material]).filter(Boolean))) as string[]
    : [];

  return (
    <div className="bg-white p-4 rounded shadow">
      <h2 className="text-lg font-semibold mb-4">Materiaal Vergelijking</h2>
      <div className="mb-4">
        <LineSelector selectedLine={selectedLine} onChange={setSelectedLine} />
      </div>
      <div className="flex space-x-4 mb-4">
        <div>
          <label htmlFor="material1" className="block text-sm font-medium text-gray-700">Materiaal 1</label>
          <select
            id="material1"
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            value={material1}
            onChange={(e) => setMaterial1(e.target.value)}
          >
            <option value="">Selecteer Materiaal 1</option>
            {materialOptions.map((material) => (
              <option key={material} value={material}>{material}</option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="material2" className="block text-sm font-medium text-gray-700">Materiaal 2</label>
          <select
            id="material2"
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            value={material2}
            onChange={(e) => setMaterial2(e.target.value)}
          >
            <option value="">Selecteer Materiaal 2</option>
            {materialOptions.map((material) => (
              <option key={material} value={material}>{material}</option>
            ))}
          </select>
        </div>
      </div>
      <div>
        {data.length > 0 ? (
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart data={data}>
              <CartesianGrid stroke="#f5f5f5" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              {material1 && <Bar dataKey={material1} barSize={20} fill="#413ea0" />}
              {material2 && <Line type="monotone" dataKey={material2} stroke="#ff7300" />}
            </ComposedChart>
          </ResponsiveContainer>
        ) : (
          <p>Geen data beschikbaar voor de geselecteerde materialen.</p>
        )}
      </div>
    </div>
  );
};

export default MaterialComparison;
