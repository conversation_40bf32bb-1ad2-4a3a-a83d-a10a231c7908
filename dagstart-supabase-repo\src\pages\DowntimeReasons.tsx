import React, { useState, useMemo } from 'react';
import { useProduction } from '@/context/ProductionContext'; // Import context
import { ProductionLine, DowntimeReason } from '@/types';
import LineSelector from '@/components/common/LineSelector';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Trash2, Plus, Edit, Save, X, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav'; // Settings nav
import { Tabs } from '@/components/ui/tabs'; // Tabs wrapper
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger, DialogDescription } from '@/components/ui/dialog'; // Dialog components
import { Label } from '@/components/ui/label'; // Label component

const DowntimeReasons: React.FC = () => {
  const {
    downtimeReasons: allDowntimeReasons, // Get all reasons from context
    addDowntimeReason,
    updateDowntimeReason,
    removeDowntimeReason,
    isFlyLocked // Get Fly Lock state
  } = useProduction();

  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingReason, setEditingReason] = useState<DowntimeReason | null>(null);
  const [formState, setFormState] = useState<Partial<DowntimeReason>>({ code: '', description: '' });

  // Get reasons for the selected line
  const currentReasons = useMemo(() => {
    return allDowntimeReasons[selectedLine] || [];
  }, [allDowntimeReasons, selectedLine]);

  const openDialog = (reason?: DowntimeReason) => {
    if (reason) {
      setEditingReason(reason);
      setFormState({ code: reason.code, description: reason.description });
    } else {
      setEditingReason(null);
      setFormState({ code: '', description: '' });
    }
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setEditingReason(null);
    setFormState({ code: '', description: '' });
  };

  const handleFormChange = (field: keyof typeof formState, value: string) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (!formState.code || !formState.description) {
        toast.error("Code en beschrijving mogen niet leeg zijn.");
        return;
    }
    
    const reasonData = {
        id: editingReason?.id || '', // ID is needed for update, generated on add
        code: formState.code.trim(),
        description: formState.description.trim()
    };

    if (editingReason) {
        // Update existing reason
        updateDowntimeReason(selectedLine, editingReason.id, reasonData);
        toast.success("Stilstand reden bijgewerkt.");
    } else {
        // Add new reason (context handler should generate ID)
        addDowntimeReason(selectedLine, reasonData);
        toast.success("Nieuwe stilstand reden toegevoegd.");
    }
    closeDialog();
  };

  const handleRemove = (reasonId: string) => {
    removeDowntimeReason(selectedLine, reasonId);
    toast.warning("Stilstand reden verwijderd.");
  };

  return (
    <div className="p-6 space-y-6">
      <Tabs value="downtime">
         <SettingsSubNav /> 
      </Tabs>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-faerch-blue">Stilstand Redenen Beheer</h1>
        <Button onClick={() => openDialog()} disabled={isFlyLocked}> {/* Disable add button */}
          <Plus className="h-4 w-4 mr-1" /> Nieuwe Reden
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <div className="dashboard-card">
           <div className="flex items-center justify-between p-4 border-b">
             <h2 className="text-xl font-semibold">Redenen per Lijn</h2>
          </div>
          <div className="p-4 space-y-4">
              <LineSelector 
                selectedLine={selectedLine} 
                onChange={setSelectedLine} 
                className="mb-4 max-w-xs" 
              />
              
              {/* Reasons List */}
              <div className="mt-4 border-t pt-4">
                  <h3 className="text-lg font-medium mb-2">Bestaande Redenen ({currentReasons.length})</h3>
                  {currentReasons.length === 0 ? (
                      <p className="text-gray-500 italic">Geen stilstand redenen gevonden voor lijn {selectedLine.toUpperCase()}.</p>
                  ) : (
                      <ul className="space-y-2">
                          {currentReasons.map((reason) => (
                              <li key={reason.id} className="flex justify-between items-center p-3 border rounded bg-gray-50">
                                 <div>
                                     <span className="font-medium">[{reason.code}]</span>
                                     <span className="ml-2">{reason.description}</span>
                                 </div>
                                 <div className="flex space-x-1">
                                     <Button 
                                        variant="ghost" 
                                        size="icon" 
                                        className="text-blue-500 hover:bg-blue-100 h-8 w-8"
                                        onClick={() => openDialog(reason)}
                                        disabled={isFlyLocked} // Disable edit button
                                     >
                                         <Edit className="h-4 w-4" />
                                     </Button>
                                     <Button 
                                        variant="ghost" 
                                        size="icon" 
                                        className="text-red-500 hover:bg-red-100 h-8 w-8"
                                        onClick={() => handleRemove(reason.id)}
                                        disabled={isFlyLocked} // Disable remove button
                                     >
                                         <Trash2 className="h-4 w-4" />
                                     </Button>
                                 </div>
                              </li>
                          ))}
                      </ul>
                  )}
              </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent>
              <DialogHeader>
                  <DialogTitle>{editingReason ? 'Stilstand Reden Bewerken' : 'Nieuwe Stilstand Reden Toevoegen'}</DialogTitle>
              </DialogHeader>
              <div className="py-4 space-y-4">
                  <div className="space-y-1">
                      <Label htmlFor="reason-code">Code</Label>
                      <Input 
                         id="reason-code"
                         value={formState.code || ''} 
                         onChange={(e) => handleFormChange('code', e.target.value)}
                         disabled={isFlyLocked} // Disable input on lock
                      />
                  </div>
                  <div className="space-y-1">
                      <Label htmlFor="reason-desc">Beschrijving</Label>
                      <Input 
                         id="reason-desc"
                         value={formState.description || ''} 
                         onChange={(e) => handleFormChange('description', e.target.value)}
                         disabled={isFlyLocked} // Disable input on lock
                      />
                  </div>
              </div>
              <DialogFooter>
                  <Button variant="outline" onClick={closeDialog}>Annuleren</Button>
                  <Button onClick={handleSave} disabled={isFlyLocked}> {/* Disable save button */}
                      {editingReason ? 'Opslaan' : 'Toevoegen'}
                  </Button>
              </DialogFooter>
          </DialogContent>
      </Dialog>
    </div>
  );
};

export default DowntimeReasons; 