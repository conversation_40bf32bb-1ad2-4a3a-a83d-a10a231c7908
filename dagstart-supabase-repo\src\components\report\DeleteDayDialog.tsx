
import React from 'react';
import { Dialog, DialogContent, DialogFooter, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ProductionLine } from '@/types';

interface DeleteDayDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dayToDelete: { line: ProductionLine; date: string } | null;
  onConfirm: () => void;
}

const DeleteDayDialog: React.FC<DeleteDayDialogProps> = ({
  open,
  onOpenChange,
  dayToDelete,
  onConfirm
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Dag Verwijderen?</DialogTitle>
          <DialogDescription>
            Weet je zeker dat je de gegevens voor de dag 
            {dayToDelete && 
               ` ${new Date(dayToDelete.date).toL<PERSON>aleDateString('nl-NL', { year: 'numeric', month: 'short', day: 'numeric'})} `
            }
            wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Annuleren</Button>
          <Button variant="destructive" onClick={onConfirm}>Verwijderen</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteDayDialog;
