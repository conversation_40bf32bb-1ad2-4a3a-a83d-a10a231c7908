import React, { useMemo } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { formatMinutes } from '@/lib/utils';
import { Target, Clock, Percent, AlertTriangle } from 'lucide-react';
import { HistoryEntry } from '@/types';
import { useProduction } from '@/context/ProductionContext';

export interface StatisticsCardsProps {
  data: HistoryEntry[];
}

export const StatisticsCards: React.FC<StatisticsCardsProps> = ({ data }) => {
  const { TARGETS } = useProduction();

  const statistics = useMemo(() => {
    const stats = {
      totalProduction: 0,
      totalDowntime: 0,
      averageYield: 0,
      totalDays: 0,
      belowTargetDays: 0,
      disruptions: 0,
      yieldValues: [] as number[],
    };

    data.forEach(entry => {
      Object.entries(entry.data).forEach(([line, lineData]) => {
        lineData.rows.forEach(row => {
          const dayTotal = 
            (Number(row.od.production) || 0) +
            (Number(row.md.production) || 0) +
            (Number(row.nd.production) || 0);
          
          stats.totalProduction += dayTotal;
          stats.totalDays++;

          const dailyTarget = TARGETS[line as keyof typeof TARGETS] * 3;
          if (dayTotal < dailyTarget) {
            stats.belowTargetDays++;
          }

          [row.od.yield, row.md.yield, row.nd.yield].forEach(y => {
            if (y && Number(y) > 0) stats.yieldValues.push(Number(y));
          });
        });

        stats.disruptions += lineData.disruptions?.length || 0;
        lineData.disruptions?.forEach(d => {
          const [hours, minutes] = (d.duration || "0:00").split(":").map(Number);
          stats.totalDowntime += (hours * 60) + (minutes || 0);
        });
      });
    });

    if (stats.yieldValues.length > 0) {
      stats.averageYield = stats.yieldValues.reduce((a, b) => a + b, 0) / stats.yieldValues.length;
    }

    return stats;
  }, [data, TARGETS]);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Totale Productie</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalProduction.toLocaleString('nl-NL')}</div>
          <p className="text-xs text-muted-foreground">
            Over {statistics.totalDays} dagen
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Totale Downtime</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {Math.floor(statistics.totalDowntime / 60)}:{(statistics.totalDowntime % 60).toString().padStart(2, '0')}
          </div>
          <p className="text-xs text-muted-foreground">
            {statistics.disruptions} storingen
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Gemiddelde Yield</CardTitle>
          <Percent className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {statistics.averageYield.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            Over {statistics.yieldValues.length} metingen
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Onder Target</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {((statistics.belowTargetDays / statistics.totalDays) * 100).toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            {statistics.belowTargetDays} van de {statistics.totalDays} dagen
          </p>
        </CardContent>
      </Card>
    </div>
  );
}; 