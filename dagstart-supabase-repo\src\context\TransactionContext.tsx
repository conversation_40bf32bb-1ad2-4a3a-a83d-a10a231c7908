import React, { createContext, useContext, useState } from 'react';

interface TransactionContextType {
  isInTransaction: boolean;
  startTransaction: () => void;
  endTransaction: () => void;
}

const TransactionContext = createContext<TransactionContextType | null>(null);

export const TransactionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isInTransaction, setIsInTransaction] = useState(false);

  const startTransaction = () => setIsInTransaction(true);
  const endTransaction = () => setIsInTransaction(false);

  return (
    <TransactionContext.Provider value={{ isInTransaction, startTransaction, endTransaction }}>
      {children}
    </TransactionContext.Provider>
  );
};

export const useTransaction = () => {
  const context = useContext(TransactionContext);
  if (!context) throw new Error('useTransaction must be used within TransactionProvider');
  return context;
};

export default TransactionContext; 