import { differenceInDays, getHours, getMinutes, parseISO, startOfDay } from 'date-fns';
import { ShiftColor } from '@/types'; // Assuming ShiftColor is defined in types

// Define shift times (inclusive start, exclusive end)
// Morning: 05:45 - 13:44
// Afternoon: 13:45 - 21:44
// Night: 21:45 - 05:44 (next day)
type Shift = 'Ochtend' | 'Middag' | 'Nacht' | 'Vrij' | 'Onbekend'; // Internal type, added Onbekend

// Team colors and their order index
const teamColors: ShiftColor[] = ['Geel', 'Blauw', 'Groen', 'Wit', 'Rood']; // Swapped Rood and Wit
const teamOrder: { [key in ShiftColor]?: number } = {
  Geel: 0,
  Blauw: 1,
  Groen: 2,
  Wit: 3,  // Updated index
  Rood: 4, // Updated index
};

// 10-day cycle states and corresponding shifts
// O1=0, O2=1, M1=2, M2=3, N1=4, N2=5, V1=6, V2=7, V3=8, V4=9
const cycleStateToShift: Shift[] = [
  'Ochtend', 'Ochtend', 'Middag', 'Middag', 'Nacht', 'Nacht', 'Vrij', 'Vrij', 'Vrij', 'Vrij'
];

// Reference date: April 10, 2025 (Month is 0-indexed, so April is 3)
const referenceDate = startOfDay(new Date(2025, 3, 10)); // April 10, 2025
// Reference state: Geel (index 0) had O1 shift (cycle state index 0) on reference date
const referenceTeamIndex = 0; // Index for Geel
const referenceCycleStateIndex = 0; // O1

/**
 * Determines the shift (Ochtend/Middag/Nacht) based on the time part of a Date object.
 * @param date The Date object.
 * @returns The shift name ('Ochtend', 'Middag', 'Nacht').
 */
function getShiftByTime(date: Date): Shift {
  const hours = getHours(date);
  const minutes = getMinutes(date);
  const timeValue = hours * 100 + minutes; // e.g., 05:45 -> 545, 13:45 -> 1345, 21:45 -> 2145

  if (timeValue >= 545 && timeValue < 1345) { // 05:45 <= time < 13:45
    return 'Ochtend';
  } else if (timeValue >= 1345 && timeValue < 2145) { // 13:45 <= time < 21:45
    return 'Middag';
  } else { // 21:45 <= time < 05:45 (next day) or 00:00 <= time < 05:45
    return 'Nacht';
  }
}

/**
 * Calculates the shift and team color for a given timestamp based on a 5-team, 2-2-2-4 rotation.
 * @param timestamp ISO string timestamp.
 * @returns Object containing the shift ('Ochtend', 'Middag', 'Nacht') and team color. Returns 'Onbekend' if calculation fails.
 */
export function getShiftInfo(timestamp: string): { shift: Shift, teamColor: ShiftColor } {
  try {
    const targetDate = parseISO(timestamp);
    const targetShift = getShiftByTime(targetDate);

    // Calculate days difference from reference date
    // Adjust for night shift crossing midnight for calculation consistency
    let calculationDate = targetDate;
    if (targetShift === 'Nacht' && getHours(targetDate) < 5) {
        // If it's a night shift before 5:45 AM, it belongs to the previous calendar day's cycle
        calculationDate = new Date(targetDate.getTime() - 24 * 60 * 60 * 1000);
    }
    const daysDifference = differenceInDays(startOfDay(calculationDate), referenceDate);

    // Calculate the cycle state index for the reference team (Blauw) on the target day
    // Add 10 to handle potential negative modulo results correctly
    const referenceTeamCurrentCycleIndex = (referenceCycleStateIndex + daysDifference % 10 + 10) % 10;

    // Find which team is working the target shift on that day
    for (let i = 0; i < teamColors.length; i++) {
      const currentTeamIndex = i;
      const currentTeamColor = teamColors[currentTeamIndex];

      // Calculate the offset of this team relative to the reference team (Geel)
      // The offset determines how many steps *back* in the team order this team is from Geel
      const teamOffset = (currentTeamIndex - referenceTeamIndex + teamColors.length) % teamColors.length;

      // Calculate the cycle state index for the current team on the target day
      // Each step *back* in the team order corresponds to a 2-day *advance* in the 10-day cycle state
      // (e.g., Blauw is 1 step after Geel, so its cycle is 2 days *behind* Geel's cycle state on the same day)
      const currentTeamCycleIndex = (referenceTeamCurrentCycleIndex + teamOffset * 2) % 10;

      // Check if this team's cycle state corresponds to the target shift
      if (cycleStateToShift[currentTeamCycleIndex] === targetShift) {
        return { shift: targetShift, teamColor: currentTeamColor };
      }
    }

    // Should not happen if logic is correct
    console.error("Could not determine team color for shift calculation.");
    return { shift: targetShift, teamColor: 'Onbekend' };

  } catch (error) {
    console.error("Error calculating shift info:", error);
    return { shift: 'Onbekend', teamColor: 'Onbekend' };
  }
}