
import React from 'react';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus, X } from 'lucide-react';
import { ProductionLine } from '@/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

const materialSchema = z.object({
  material: z.string().min(1, 'Materiaal naam is verplicht'),
});

export type MaterialFormValues = z.infer<typeof materialSchema>;

interface MaterialGroupFormProps {
  form: UseFormReturn<MaterialFormValues>;
  onSubmit: (values: MaterialFormValues) => void;
  groupMaterials: string[];
  lineGroup: {
    name: string;
    lines: ProductionLine[];
  };
  onRemoveMaterial: (material: string) => void;
  lineNames: string;
}

const MaterialGroupForm: React.FC<MaterialGroupFormProps> = ({ 
  form, 
  onSubmit, 
  groupMaterials, 
  lineGroup,
  onRemoveMaterial,
  lineNames
}) => {
  return (
    <div className="space-y-6">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4"
        >
          <div className="flex flex-col md:flex-row items-start gap-4">
            <FormField
              control={form.control}
              name="material"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Materiaal naam</FormLabel>
                  <FormControl>
                    <Input placeholder="Voer materiaal naam in" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <Button type="submit" className="mt-8 whitespace-nowrap">
              <Plus className="mr-2 h-4 w-4" /> Toevoegen voor alle {lineNames} lijnen
            </Button>
          </div>
        </form>
      </Form>
      
      <div className="border rounded-md p-4">
        <h3 className="text-lg font-medium mb-3">Materialen voor {lineNames}</h3>
        
        {groupMaterials.length === 0 ? (
          <p className="text-gray-500 italic">Geen materialen toegevoegd</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
            {groupMaterials.map((material, index) => (
              <div
                key={index}
                className="flex items-center justify-between bg-gray-50 p-3 rounded-md border"
              >
                <span>{material}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onRemoveMaterial(material)}
                  className="h-8 w-8 text-red-500 hover:bg-red-50"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-md border">
        <p>Alle materialen toegevoegd aan {lineNames} worden automatisch beschikbaar gemaakt voor 
        alle lijnen in deze groep.</p>
      </div>
    </div>
  );
};

export default MaterialGroupForm;
