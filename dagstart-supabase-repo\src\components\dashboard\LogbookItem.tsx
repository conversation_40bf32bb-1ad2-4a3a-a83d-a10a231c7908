import React, { useState } from 'react';
import { LogbookEntry } from '@/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from "@/components/ui/checkbox"; // Import Checkbox
import { Label } from "@/components/ui/label"; // Import Label
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { Trash2, Pencil, X } from 'lucide-react';

interface LogbookItemProps {
  entry: LogbookEntry;
  onEdit: (entry: LogbookEntry) => void;
  onDeleteConfirm: (entry: LogbookEntry) => void; // Renamed for clarity
}

const CHECKBOX_COLORS = ["Blauw", "Geel", "Groen", "Rood", "Wit"] as const;
type CheckboxColor = typeof CHECKBOX_COLORS[number];

const LogbookItem: React.FC<LogbookItemProps> = ({ entry, onEdit, onDeleteConfirm }) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [checkboxes, setCheckboxes] = useState<Record<CheckboxColor, boolean>>(
    Object.fromEntries(CHECKBOX_COLORS.map(color => [color, false])) as Record<CheckboxColor, boolean>
  );

  const handleCheckboxChange = (color: CheckboxColor, checked: boolean | 'indeterminate') => {
    if (typeof checked === 'boolean') {
      setCheckboxes(prev => ({ ...prev, [color]: checked }));
    }
  };

  const resetDeleteState = () => {
    setShowDeleteConfirm(false);
    setCheckboxes(Object.fromEntries(CHECKBOX_COLORS.map(color => [color, false])) as Record<CheckboxColor, boolean>);
  };

  const allCheckboxesChecked = Object.values(checkboxes).every(Boolean);

  const getPriorityBadgeVariant = (priority: LogbookEntry['priority']): "default" | "secondary" | "destructive" | "outline" | null | undefined => {
    switch (priority) {
      case 'Hoog': return 'destructive';
      case 'Middel': return 'secondary';
      case 'Laag': return 'outline';
      default: return 'default';
    }
  };

  return (
    <div className="text-sm border-b pb-2 last:border-b-0 flex justify-between items-start gap-2 group">
      {/* Entry Details */}
      <div className="flex-grow">
        <div className="flex justify-between items-start mb-1">
          <Badge variant={getPriorityBadgeVariant(entry.priority)} className="text-xs px-1.5 py-0.5">
            {entry.priority}
          </Badge>
          <span className="text-xs text-gray-400 flex-shrink-0 ml-2">
            {format(new Date(entry.timestamp), 'dd-MM HH:mm', { locale: nl })}
          </span>
        </div>
        <p className="text-xs text-gray-500 mb-1">Locatie: {entry.location || 'N/A'}</p>
        <p className="text-gray-700 whitespace-pre-wrap">{entry.text}</p>

        {/* Checkbox Confirmation Area (conditionally rendered) */}
        {showDeleteConfirm && (
          <div className="mt-3 p-3 border border-red-200 rounded bg-red-50/50">
            <p className="text-xs font-medium text-red-700 mb-2">Vink alle kleuren aan om verwijderen te bevestigen:</p>
            <div className="grid grid-cols-3 sm:grid-cols-5 gap-x-4 gap-y-2">
              {CHECKBOX_COLORS.map((color) => (
                <div key={color} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${entry.id}-${color}`}
                    checked={checkboxes[color]}
                    onCheckedChange={(checked) => handleCheckboxChange(color, checked)}
                    aria-label={`Checkbox ${color}`}
                  />
                  <Label htmlFor={`${entry.id}-${color}`} className="text-xs font-medium">
                    {color}
                  </Label>
                </div>
              ))}
            </div>
            <div className="flex justify-end gap-2 mt-3">
               <Button variant="ghost" size="sm" onClick={resetDeleteState} className="text-xs h-7">
                 <X className="w-3 h-3 mr-1"/> Annuleren
               </Button>
               <Button
                 variant="destructive"
                 size="sm"
                 disabled={!allCheckboxesChecked}
                 onClick={() => onDeleteConfirm(entry)}
                 className="text-xs h-7"
               >
                 <Trash2 className="w-3 h-3 mr-1"/> Definitief Verwijderen
               </Button>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons Area */}
      {!showDeleteConfirm && (
         <div className="flex flex-col items-center space-y-1 opacity-0 group-hover:opacity-100 transition-opacity">
           {/* Edit Button */}
           <Button variant="ghost" size="icon" className="h-6 w-6 text-blue-500 hover:bg-blue-100 rounded-full flex-shrink-0" onClick={() => onEdit(entry)}>
             <Pencil className="h-3.5 w-3.5" />
           </Button>
           {/* Initial Delete Button */}
           <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500 hover:bg-red-100 rounded-full flex-shrink-0" onClick={() => setShowDeleteConfirm(true)}>
             <Trash2 className="h-4 w-4" />
           </Button>
         </div>
      )}
    </div>
  );
};

export default LogbookItem;