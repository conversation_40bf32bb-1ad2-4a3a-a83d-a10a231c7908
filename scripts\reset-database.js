// Script om de database te resetten via de Supabase API
const { createClient } = require('@supabase/supabase-js');

// Supabase configuratie
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://dbsztlsxgbheifrpmsaa.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Supabase service key is niet ingesteld. Stel REACT_APP_SUPABASE_SERVICE_KEY in als omgevingsvariabele.');
  process.exit(1);
}

// Supabase client initialiseren met service key
const supabase = createClient(supabaseUrl, supabaseKey);

async function resetDatabase() {
  console.log('Start met het resetten van de database...');

  try {
    // Verwijder alle notificaties
    console.log('Notificaties tabel leegmaken...');
    const { error: truncateNotificationsError } = await supabase.rpc('truncate_notifications');
    
    if (truncateNotificationsError) {
      console.error('Fout bij het leegmaken van de notificaties tabel:', truncateNotificationsError);
      
      // Alternatieve methode als de RPC functie niet bestaat
      console.log('Proberen met directe delete query...');
      const { error: deleteNotificationsError } = await supabase
        .from('notifications')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000');
        
      if (deleteNotificationsError) {
        console.error('Fout bij het verwijderen van notificaties:', deleteNotificationsError);
      } else {
        console.log('Notificaties succesvol verwijderd.');
      }
    } else {
      console.log('Notificaties tabel succesvol leeggemaakt.');
    }

    // Verwijder alle todo's
    console.log('Todos tabel leegmaken...');
    const { error: truncateTodosError } = await supabase.rpc('truncate_todos');
    
    if (truncateTodosError) {
      console.error('Fout bij het leegmaken van de todos tabel:', truncateTodosError);
      
      // Alternatieve methode als de RPC functie niet bestaat
      console.log('Proberen met directe delete query...');
      const { error: deleteTodosError } = await supabase
        .from('todos')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000');
        
      if (deleteTodosError) {
        console.error('Fout bij het verwijderen van todos:', deleteTodosError);
      } else {
        console.log('Todos succesvol verwijderd.');
      }
    } else {
      console.log('Todos tabel succesvol leeggemaakt.');
    }

    // Verwijder alle bestanden in de images bucket
    console.log('Bestanden in de images bucket verwijderen...');
    const { data: files, error: listError } = await supabase
      .storage
      .from('images')
      .list();

    if (listError) {
      console.error('Fout bij het ophalen van bestanden uit de images bucket:', listError);
    } else if (files && files.length > 0) {
      const filePaths = files.map(file => file.name);
      const { error: deleteError } = await supabase
        .storage
        .from('images')
        .remove(filePaths);

      if (deleteError) {
        console.error('Fout bij het verwijderen van bestanden uit de images bucket:', deleteError);
      } else {
        console.log(`${filePaths.length} bestanden verwijderd uit de images bucket.`);
      }
    } else {
      console.log('Geen bestanden gevonden in de images bucket.');
    }

    console.log('Database reset voltooid!');
  } catch (error) {
    console.error('Onverwachte fout bij het resetten van de database:', error);
  }
}

// Voer de functie uit
resetDatabase();
