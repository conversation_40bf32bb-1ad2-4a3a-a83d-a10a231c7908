// Script om alle todo's te verwijderen
const { createClient } = require('@supabase/supabase-js');

// Supabase configuratie
const supabaseUrl = 'https://dbsztlsxgbheifrpmsaa.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRic3p0bHN4Z2JoZWlmcnBtc2FhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTI5MjM5NzcsImV4cCI6MjAyODQ5OTk3N30.Rl-QQhQdYpKGQC9GgQZYDRnNHzXN-RcGlbEVXzQBDQs';

// Supabase client initialiseren
const supabase = createClient(supabaseUrl, supabaseKey);

async function clearTodos() {
  console.log('Alle todo\'s verwijderen...');

  try {
    const { error } = await supabase
      .from('todos')
      .delete()
      .is('id', 'not.null'); // Verwijder alle rijen

    if (error) {
      console.error('Fout bij het verwijderen van todo\'s:', error);
    } else {
      console.log('Alle todo\'s zijn succesvol verwijderd!');
    }
  } catch (error) {
    console.error('Onverwachte fout:', error);
  }
}

// Voer de functie uit
clearTodos();
