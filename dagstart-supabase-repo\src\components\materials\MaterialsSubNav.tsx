import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Box, GitCompareArrows } from 'lucide-react'; // Icoontjes toegevoegd
import { cn } from '@/lib/utils';

export const MaterialsSubNav = ({ className }: { className?: string }) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Bepaal actieve sub-tab op basis van huidige pad
  let activeTab = 'management'; // Default naar beheer
  if (location.pathname.endsWith('/comparison')) {
    activeTab = 'comparison';
  }

  const handleNavigate = (value: string) => {
    navigate(`/settings/materials/${value}`);
  };

  return (
    <TabsList className={cn("grid w-full grid-cols-2 mb-6", className)}>
      <TabsTrigger
        value="management"
        className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
        onClick={() => handleNavigate('management')}
        data-state={activeTab === 'management' ? 'active' : 'inactive'}
      >
        <Box className="h-4 w-4" />
        Materiaal Beheer
      </TabsTrigger>
      <TabsTrigger
        value="comparison"
        className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
        onClick={() => handleNavigate('comparison')}
        data-state={activeTab === 'comparison' ? 'active' : 'inactive'}
      >
        <GitCompareArrows className="h-4 w-4" />
        Materiaal Vergelijking
      </TabsTrigger>
    </TabsList>
  );
}; 