# Cirrec Dag start

This project is a web application designed as a production dashboard for Cirrec.

## Developed By

Cirrec Netherlands B.V.
Roelofshoeveweg 43
6921 RH Duiven
The Netherlands
+31 851 052 160
[https://www.cirrec.nl](https://www.cirrec.nl)

## Tech Stack

This project is built using the following technologies:

*   **Language:** [TypeScript](https://www.typescriptlang.org/docs/) - A typed superset of JavaScript.
*   **Framework:** [React](https://react.dev/) - A JavaScript library for building user interfaces.
*   **Build Tool:** [Vite](https://vitejs.dev/guide/) - A fast frontend build tool.
*   **Routing:** [React Router](https://reactrouter.com/en/main) - Handles client-side navigation within the single-page application.
*   **Styling:** [Tailwind CSS](https://tailwindcss.com/docs/installation) - A utility-first CSS framework.
*   **UI Components:** [shadcn/ui](https://ui.shadcn.com/docs) - Re-usable components built using Radix UI and Tailwind CSS. Configuration is in `components.json`.
*   **State Management:** [React Context API](https://react.dev/learn/passing-data-deeply-with-context) - Used for managing global state (see `src/context/` and `src/hooks/use-production-state.ts`).
*   **Backend & Real-time:** [Supabase](https://supabase.com/docs) - Currently used as the backend-as-a-service for data storage (via `src/lib/supabase-storage.ts` interacting with the `local_storage_data` table) and real-time data synchronization (via Supabase Realtime subscriptions configured in `src/hooks/use-production-state.ts`).
*   **Date Handling:** [date-fns](https://date-fns.org/docs/Getting-Started) - For date formatting and manipulation.
*   **Icons:** [Lucide React](https://lucide.dev/guide/packages/lucide-react) - Icon library.
*   **Notifications:** [Sonner](https://sonner.emilkowal.ski/introduction) - Toast notifications.

## Running Locally

1.  Install dependencies:
    ```bash
    npm install
    ```
2.  Start the development server:
    ```bash
    npm run dev
    ```
    *(Note: Ensure you have appropriate Supabase environment variables set up if running against a Supabase backend).*

## Backend Migration (from Supabase to Custom SQL Database)

The current implementation relies heavily on Supabase for data persistence and real-time updates. To migrate to a custom backend with an SQL database:

1.  **Create a Backend API:**
    *   Build a separate backend application (e.g., using Node.js/Express, Python/Flask/Django, C#/ASP.NET Core, etc.).
    *   This API needs to connect to your SQL database.
    *   Implement API endpoints (e.g., REST or GraphQL) to perform CRUD (Create, Read, Update, Delete) operations on your data (production rows, disruptions, history, settings, etc.). These endpoints will replace the direct Supabase calls.

2.  **Modify Frontend Data Logic:**
    *   Replace the Supabase client setup (`src/lib/supabase-client.ts`).
    *   Rewrite the data interaction logic in `src/lib/supabase-storage.ts` (or create a new API client module) to call your new backend API endpoints instead of Supabase functions (`supabase.from(...).select()`, `.insert()`, `.update()`, `.delete()`).
    *   Update the state management hook (`src/hooks/use-production-state.ts`) to use these new API-calling functions.

3.  **Implement Real-time Updates (Optional but Recommended):**
    *   Supabase Realtime provides live updates. To replicate this, your custom backend API needs a real-time communication mechanism (e.g., WebSockets using libraries like Socket.IO, or Server-Sent Events).
    *   The backend should push updates to connected frontend clients when data changes in the SQL database.
    *   The frontend (`src/hooks/use-production-state.ts`) needs to be updated to connect to this new real-time service and handle incoming update messages to keep the UI synchronized across different users.

## Hosting Configuration (SPA Routing)

This is a Single Page Application (SPA). When hosting on a server other than Netlify, you need to configure the webserver to handle client-side routing correctly.

*   **Problem:** When a user directly accesses a path like `/history` or refreshes the page on such a path, the server might look for a file named `history` instead of serving the main application entry point (`index.html`). This causes a 404 "Not Found" error.
*   **Solution:** Configure your web server (e.g., Apache, Nginx, IIS, Caddy, or a custom Node.js server) to redirect all requests for non-existent files/paths to your root `index.html` file. This allows the React Router library within your JavaScript bundle to handle the routing on the client-side.
*   **Example Concept (Specific rules vary by server):**
    *   For Netlify, we used `netlify.toml` with `[[redirects]] from = "/*" to = "/index.html" status = 200`.
    *   For Apache, you might use `.htaccess` with `mod_rewrite` rules.
    *   For Nginx, you might use `try_files $uri $uri/ /index.html;` within a `location` block.
    *   Consult the documentation for your specific web server software on how to implement SPA fallback or URL rewrite rules.
