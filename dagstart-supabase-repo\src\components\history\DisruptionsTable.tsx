import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ProductionLine, HistoryEntry } from '@/types';

export interface DisruptionsTableProps {
  data: HistoryEntry[];
  selectedLine: ProductionLine | 'all';
}

export const DisruptionsTable: React.FC<DisruptionsTableProps> = ({ data, selectedLine }) => {
  const flattenedDisruptions = data.flatMap(entry =>
    Object.entries(entry.data).flatMap(([line, lineData]) => {
      if (selectedLine !== 'all' && line !== selectedLine) return [];
      
      return (lineData.disruptions || []).map(disruption => ({
        ...disruption,
        line: line as ProductionLine,
        date: entry.timestamp
      }));
    })
  );

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            {selectedLine === 'all' && <TableHead>Lijn</TableHead>}
            <TableHead className="w-[250px]">Apparaat</TableHead>
            <TableHead>Omschrijving</TableHead>
            <TableHead className="w-[100px]">Duur</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {flattenedDisruptions.map((disruption, index) => (
            <TableRow key={index}>
              {selectedLine === 'all' && (
                <TableCell className="font-medium">{disruption.line.toUpperCase()}</TableCell>
              )}
              <TableCell>{disruption.equipment || '-'}</TableCell>
              <TableCell>{disruption.description || '-'}</TableCell>
              <TableCell>{disruption.duration || '-'}</TableCell>
            </TableRow>
          ))}
          {flattenedDisruptions.length === 0 && (
            <TableRow>
              <TableCell colSpan={selectedLine === 'all' ? 4 : 3} className="text-center text-muted-foreground">
                Geen storingen gevonden
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}; 