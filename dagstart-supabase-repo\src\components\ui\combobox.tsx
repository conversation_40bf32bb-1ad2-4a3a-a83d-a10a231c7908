'use client'

import * as React from 'react'
import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

export interface ComboboxOption {
  value: string
  label: string
}

interface ComboboxProps {
  options: ComboboxOption[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  notFoundMessage?: string
  id?: string
  disabled?: boolean
}

export function Combobox({
  options,
  value,
  onValueChange,
  placeholder = 'Select option...',
  searchPlaceholder = 'Search option...',
  notFoundMessage = 'No option found.',
  id,
  disabled = false
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState(value || '')

  // Update input value when external value changes
  React.useEffect(() => {
    setInputValue(value || '')
  }, [value])

  const handleSelect = (currentValue: string) => {
    const newValue = currentValue === value ? '' : currentValue
    setInputValue(newValue)
    onValueChange?.(newValue)
    setOpen(false)
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value
    setInputValue(newValue)
    // Directly propagate input change if needed, or only on blur/select
    onValueChange?.(newValue) 
  }
  
   const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && open) {
      // Allow selecting the currently highlighted item in Command on Enter
      // The Command component handles this internally usually
    } else if (event.key === 'Enter' && !open) {
       // If popover is closed, Enter could confirm the typed input
       // (Optional: depends on desired behavior)
       // onValueChange?.(inputValue);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          id={id}
          disabled={disabled}
        >
          {value
            ? options.find((option) => option.value === value)?.label
            : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command shouldFilter={true}> 
          {/* Use CommandInput inside Command for built-in filtering */}
          <CommandInput 
             placeholder={searchPlaceholder} 
             value={inputValue} // Control the input value
             onValueChange={setInputValue} // Let Command handle internal filtering based on display value
             onKeyDown={handleKeyDown} // Handle Enter key if necessary
          />
          <CommandList>
            <CommandEmpty>{notFoundMessage}</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value} // Value used for filtering/selection
                  onSelect={handleSelect} // Use Command's onSelect
                  disabled={disabled}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      value === option.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
              {/* Optionally, add an item to confirm free text input? */}
             {/* 
              {inputValue && !options.some(opt => opt.value.toLowerCase() === inputValue.toLowerCase()) && (
                  <CommandItem 
                    value={inputValue} 
                    onSelect={() => handleSelect(inputValue)}
                  >
                     Create "{inputValue}"
                  </CommandItem>
               )}
              */}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
} 