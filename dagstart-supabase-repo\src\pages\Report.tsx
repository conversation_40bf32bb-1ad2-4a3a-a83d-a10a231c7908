import React, { useState, useEffect, useMemo, useRef, FC } from 'react';
import { useProduction } from '@/context/ProductionContext';
import { format, parse } from 'date-fns'; // Added parse
import { nl } from 'date-fns/locale';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import LineSelector from '@/components/common/LineSelector';
import ShiftForm from '@/components/report/ShiftForm';
import DaySelector from '@/components/report/DaySelector';
// import BreakdownForm from '@/components/report/BreakdownForm'; // Removed
import DaySummary from '@/components/report/DaySummary';
// import IndependentDisruptionInput from '@/components/report/IndependentDisruptionInput'; // Removed import
import DateSelector from '@/components/report/DateSelector';
import DeleteDayDialog from '@/components/report/DeleteDayDialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Check, AlertTriangle, Save, Printer, Edit, Trash2, Plus, X, Loader2 } from 'lucide-react';
// Corrected type imports
import { ProductionLine, ProductionRow, LineData, EquipmentEntry, ProductionShiftData, Disruption, ProductionData, DisruptionInput } from '@/types';
import { loadItem, saveItem, removeItem } from '@/lib/local-storage';
import { useTransaction } from '@/context/TransactionContext';
import { useReactToPrint } from 'react-to-print';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogContent } from '@/components/ui/alert-dialog';

// Import refactored components and utils
import {
  useReportForm,
  createInitialShiftData,
  // formatForDisplay, // Using local version now
  parseAndValidateNumber
} from '@/hooks/report/useReportForm';
import ShiftInputDialog from '@/components/report/ShiftInputDialog';

const ReportPage: FC = () => {
  const {
    productionData,
    updateProductionRow,
    addProductionRow,
    equipmentOptions,
    materialOptions,
    removeProductionRow,
    addToHistory,
    TARGETS,
    YIELD_TARGETS,
    clearProductionData,
    archiveAndClearIncidents,
    historyData,
    addDisruption,
    updateDisruption,
    removeDisruption,
    isFlyLocked,
    independentDisruptions,
    updateIndependentDisruption
  } = useProduction();

  const { startTransaction, endTransaction } = useTransaction();
  const [syncStatus, setSyncStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const [lastAutoSave, setLastAutoSave] = useState<string | null>(null);
  const [resetTrigger, setResetTrigger] = useState(0);

  // Destructure handleShiftChange from useReportForm
  const {
    selectedLine,
    activeRow,
    activeRowIndex,
    lineData,
    handleLineChange,
    handleRowChange, // This handles index change for DaySelector
    handleShiftChange, // This handles changes within ShiftForm
    handleAddDay,
    handleDateSelect, // This handles initial date selection
    handleDeleteDay,
    handleConfirmDeleteDay,
    calculateTotalProduction, // This might be defined inside useReportForm now
    dateDialogOpen,
    setDateDialogOpen,
    selectedDate, // This might be managed within useReportForm
    setSelectedDate, // This might be managed within useReportForm
    showDeleteDayDialog,
    setShowDeleteDayDialog,
    dayToDelete,
    isShiftInputOpen,
    setIsShiftInputOpen,
    pendingRowDate,
    handleShiftInputSubmit,
    handleEditDay,
    editingRowData,
    lastFieldChangeTime,
  } = useReportForm();

  // Lokale state voor opslagstatus
  const [isModified, setIsModified] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveVariant, setSaveVariant] = useState<'default' | 'success' | 'error'>('default');
  const [saveText, setSaveText] = useState('Opslaan');

  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({ from: undefined, to: undefined });
  const [isEditingRow, setIsEditingRow] = useState(false);
  const [isEditingDisruption, setIsEditingDisruption] = useState(false);
  const [editingDisruptionData, setEditingDisruptionData] = useState<Disruption | null>(null);
  const [editingRowDataLocal, setEditingRowDataLocal] = useState<ProductionRow | null>(null);

  const componentRef = useRef<HTMLDivElement>(null);

  // Print functionaliteit
  const handlePrint = useReactToPrint({
    documentTitle: `Rapport ${selectedLine}`,
    // content: () => componentRef.current, // Removed invalid property
  });

  // Autosave effect (Placeholder)
  useEffect(() => {
    if (!lastFieldChangeTime || !activeRow) return;
    setIsModified(true);
    const autoSaveTimer = setTimeout(() => {
      console.log('Autosave triggered (placeholder)');
    }, 30000);
    return () => { clearTimeout(autoSaveTimer); };
  }, [lastFieldChangeTime, activeRow]);

  // Waarschuwing bij verlaten van pagina
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      const hasUnsavedDisruptions = independentDisruptions[selectedLine]?.some(d => d !== null);
      if (isModified || hasUnsavedDisruptions) {
        const message = "Er zijn mogelijk onopgeslagen wijzigingen. Weet u zeker dat u deze pagina wilt verlaten?";
        e.returnValue = message; return message;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isModified, independentDisruptions, selectedLine]);

  // Nieuwe berekeningen voor DaySummary over alle dagen
  const totalProductionForAllDays = useMemo(() => {
    if (!lineData) return 0;
    return lineData.rows.reduce((total, row) => total + (Number(row.od.production) || 0) + (Number(row.md.production) || 0) + (Number(row.nd.production) || 0), 0);
  }, [lineData]);
  const totalTargetForAllDays = useMemo(() => {
    if (!lineData || !TARGETS[selectedLine]) return 0;
    return (TARGETS[selectedLine] * 3) * lineData.rows.length;
  }, [lineData, selectedLine, TARGETS]);
  const averageYieldForAllDays = useMemo(() => {
    if (!lineData || lineData.rows.length === 0) return 0;
    let totalYield = 0; let validShiftCount = 0;
    lineData.rows.forEach(row => {
      const yields = [Number(row.od.yield) || 0, Number(row.md.yield) || 0, Number(row.nd.yield) || 0];
      yields.forEach(y => { if (y > 0) { totalYield += y; validShiftCount++; } });
    });
    return validShiftCount === 0 ? 0 : totalYield / validShiftCount;
  }, [lineData]);
  const currentYieldTarget = useMemo(() => YIELD_TARGETS?.[selectedLine] || 0, [selectedLine, YIELD_TARGETS]);

  // --- Refactored handleSave FUNCTIE ---
  const handleSave = async () => {
    const allLines: ProductionLine[] = ['tl1', 'tl2', 'p1', 'p2', 'p3'];
    let totalRowsProcessed = 0;
    setIsSaving(true); setSaveText(`Verwerken...`); setSaveVariant('default');
    let errorCount = 0; const errors: string[] = [];

    for (const line of allLines) {
      const lineDataToProcess = productionData[line];
      if (!lineDataToProcess || !lineDataToProcess.rows || lineDataToProcess.rows.length === 0) continue;
      const rowsToProcess = [...lineDataToProcess.rows];
      totalRowsProcessed += rowsToProcess.length;
      const startDate = rowsToProcess.reduce((min, row) => new Date(row.date) < new Date(min) ? row.date : min, rowsToProcess[0].date);
      const endDate = rowsToProcess.reduce((max, row) => new Date(row.date) > new Date(max) ? row.date : max, rowsToProcess[0].date);
      const lineDataForHistory: LineData = { rows: rowsToProcess, disruptions: [], materials: lineDataToProcess.materials || [], equipmentOptions: lineDataToProcess.equipmentOptions || {} };
      const dataForHistory: Partial<ProductionData> = { [line]: lineDataForHistory };
      console.log(`Archiveren voor lijn ${line}: ${rowsToProcess.length} rijen...`);
      try {
        addToHistory({ startDate, endDate, data: dataForHistory as ProductionData, line });
        console.log(`Productie rijen wissen voor lijn ${line}...`);
        // Use updateProductionRow to clear rows for the specific line
        updateProductionRow(line, '', { rows: [] } as any); // Still a bit hacky, assumes update handles empty date for clearing
        removeItem(`production_${line}`); removeItem(`breakdowns_${line}`);
      } catch (error) { console.error(`Fout bij archiveren of wissen van data voor lijn ${line}:`, error); errors.push(`Fout bij verwerken van lijn ${line}: ${error}`); errorCount++; }
    }
    if (localStorage.getItem('saveVK') === 'true') {
        try { console.log('Archiveren en wissen van Veiligheid & Kwaliteit meldingen...'); archiveAndClearIncidents(); }
        catch (error) { console.error('Fout bij archiveren van V&K meldingen:', error); errors.push(`Fout bij archiveren V&K meldingen: ${error}`); errorCount++; }
    }
    setIsSaving(false);
    if (errorCount === 0 && totalRowsProcessed > 0) { setSaveText('Alles Verwerkt'); setSaveVariant('success'); toast.success(`${totalRowsProcessed} productie rij(en) succesvol gearchiveerd en gewist over alle lijnen.`); }
    else if (errorCount > 0) { setSaveText('Verwerken Mislukt'); setSaveVariant('error'); toast.error(`Verwerken mislukt met ${errorCount} fout(en).`, { description: errors.join(' '), }); }
    else { toast.info("Geen nieuwe productierijen om te verwerken voor alle lijnen."); setSaveText('Opslaan'); }
    setTimeout(() => { setSaveVariant('default'); setSaveText('Opslaan'); }, 3000);
  };
  // --- EINDE handleSave FUNCTIE ---

  // Filter data based on line and date range
  const filteredRows = useMemo(() => {
    if (!lineData) return [];
    if (!dateRange.from || !dateRange.to) return [...lineData.rows];
    return lineData.rows.filter(row => { const date = new Date(row.date); return date >= dateRange.from! && date <= dateRange.to!; });
  }, [lineData, dateRange]);

  // --- Example Handlers for Editing/Deleting from Report ---
  const openEditRowDialog = (row: ProductionRow) => { setEditingRowDataLocal(row); setIsEditingRow(true); };
  const closeEditRowDialog = () => { setIsEditingRow(false); setEditingRowDataLocal(null); };
  const handleSaveRow = () => {
    if (editingRowDataLocal) {
      const dateToUpdate = editingRowDataLocal.date;
      if (!dateToUpdate) { toast.error("Kan rij niet opslaan: datum ontbreekt."); return; }
      updateProductionRow(selectedLine, dateToUpdate, editingRowDataLocal);
      toast.success("Productierij bijgewerkt.");
    }
    closeEditRowDialog();
  };
  const handleDeleteRow = (date: string) => { removeProductionRow(selectedLine, date); toast.warning("Productierij verwijderd."); };
  const openEditDisruptionDialog = (disruption: Disruption) => { setEditingDisruptionData(disruption); setIsEditingDisruption(true); };
  const closeEditDisruptionDialog = () => { setIsEditingDisruption(false); setEditingDisruptionData(null); };
  const handleSaveDisruption = () => {
    if (editingDisruptionData) {
      const updates: Partial<Disruption> = { /* get changed fields from dialog */ };
      updateDisruption(selectedLine, editingDisruptionData.id, updates);
      toast.success("Storing bijgewerkt.");
    }
    closeEditDisruptionDialog();
  };
  const handleDeleteDisruption = (id: string) => { removeDisruption(selectedLine, id); };

  // Format function for display (can be moved to utils)
  const formatForDisplayLocal = (value: string | number | undefined): string => {
      if (value === undefined || value === null) return '';
      return String(value).replace('.', ',');
  };


  return (
    <div className="animate-fade-in space-y-4 pb-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-2">
        <LineSelector selectedLine={selectedLine} onChange={handleLineChange} />
        <div className="flex items-center gap-2">
          <Button onClick={handleSave} size="sm" disabled={isSaving || isFlyLocked}>
            {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : saveVariant === 'success' ? <Check className="h-4 w-4 mr-2" /> : saveVariant === 'error' ? <AlertTriangle className="h-4 w-4 mr-2 text-red-500" /> : <Save className="h-4 w-4 mr-2" />}
            {saveText}
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <DaySummary
          totalProduction={totalProductionForAllDays}
          target={totalTargetForAllDays}
          averageYield={averageYieldForAllDays}
          yieldTarget={currentYieldTarget}
        />
      </div>

      {/* REMOVED: Independent Disruption Input Section (Moved to OverdrachtPage) */}

      <div className="flex flex-col md:flex-row gap-6 mb-6">
        <div className="md:basis-1/3 border rounded-md p-4 bg-white shadow-sm flex flex-col">
          <div className="flex justify-between items-center mb-4 flex-shrink-0">
            <h3 className="text-lg font-semibold">Dagen</h3>
            <Button onClick={handleAddDay} size="sm" variant="outline">
              <Plus className="h-4 w-4 mr-1" /> Nieuwe Dag
            </Button>
          </div>
           <div className="flex-grow overflow-y-auto pr-2">
             <DaySelector
               lineData={lineData || { rows: [], materials: [], disruptions: [], equipmentOptions: {} }}
               activeRowIndex={activeRowIndex}
               onRowChange={handleRowChange} // Correct handler for index change
               onDeleteDay={handleDeleteDay}
               onEditDay={handleEditDay}
               selectedLine={selectedLine}
             />
           </div>
        </div>

        <div className="md:basis-2/3 border rounded-md p-4 bg-white shadow-sm">
          {activeRow ? (
            <>
              <h3 className="text-lg font-semibold mb-4">
                Details voor {format(new Date(activeRow.date + 'T00:00:00'), 'EEEE d MMMM yyyy', { locale: nl })}
              </h3>
              <Tabs defaultValue="od" className="w-full">
                <TabsList className="grid w-full grid-cols-3 mb-4">
                  <TabsTrigger value="od">Ochtend</TabsTrigger>
                  <TabsTrigger value="md">Middag</TabsTrigger>
                  <TabsTrigger value="nd">Nacht</TabsTrigger>
                </TabsList>
                <TabsContent value="od">
                  <ShiftForm
                    shift="od"
                    shiftLabel="Ochtenddienst"
                    shiftData={activeRow.od}
                    onShiftChange={handleShiftChange} // Correct handler
                    materialOptions={materialOptions[selectedLine] || []}
                    isDisabled={false}
                    formatForDisplay={formatForDisplayLocal} // Use local formatter
                  />
                </TabsContent>
                <TabsContent value="md">
                  <ShiftForm
                    shift="md"
                    shiftLabel="Middagdienst"
                    shiftData={activeRow.md}
                    onShiftChange={handleShiftChange} // Correct handler
                    materialOptions={materialOptions[selectedLine] || []}
                    isDisabled={false}
                    formatForDisplay={formatForDisplayLocal} // Use local formatter
                  />
                </TabsContent>
                <TabsContent value="nd">
                  <ShiftForm
                    shift="nd"
                    shiftLabel="Nachtdienst"
                    shiftData={activeRow.nd}
                    onShiftChange={handleShiftChange} // Correct handler
                    materialOptions={materialOptions[selectedLine] || []}
                    isDisabled={false}
                    formatForDisplay={formatForDisplayLocal} // Use local formatter
                  />
                </TabsContent>
              </Tabs>
              {/* REMOVED: Rendering of breakdowns within day accordion */}
            </>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              Selecteer een dag of voeg een nieuwe dag toe.
            </div>
          )}
        </div>
      </div>

      {/* Removed Standalone BreakdownForm component */}
      {/* Removed DateSelector */}

      <ShiftInputDialog
        open={isShiftInputOpen}
        onOpenChange={setIsShiftInputOpen}
        onSubmit={handleShiftInputSubmit}
        selectedDate={pendingRowDate}
        selectedLine={selectedLine}
        materialOptions={materialOptions[selectedLine] || []}
        target={TARGETS[selectedLine] ? TARGETS[selectedLine] * 3 : undefined}
        initialData={editingRowData}
      />

      <DeleteDayDialog
        open={showDeleteDayDialog}
        onOpenChange={setShowDeleteDayDialog}
        onConfirm={handleConfirmDeleteDay}
        dayToDelete={dayToDelete}
      />

      {/* Dialogs for editing rows/disruptions (placeholders) */}
      <Dialog open={isEditingRow} onOpenChange={setIsEditingRow}>
         <DialogContent>
           <DialogHeader><DialogTitle>Bewerk Productierij</DialogTitle></DialogHeader>
           <p>Bewerk formulier hier...</p>
             <DialogFooter>
               <Button variant="outline" onClick={closeEditRowDialog}>Annuleren</Button>
               <Button onClick={handleSaveRow}>Opslaan</Button>
             </DialogFooter>
         </DialogContent>
      </Dialog>

      <Dialog open={isEditingDisruption} onOpenChange={setIsEditingDisruption}>
         <DialogContent>
           <DialogHeader><DialogTitle>Bewerk Storing</DialogTitle></DialogHeader>
            <p>Bewerk formulier hier...</p>
             <DialogFooter>
               <Button variant="outline" onClick={closeEditDisruptionDialog}>Annuleren</Button>
               <Button onClick={handleSaveDisruption}>Opslaan</Button>
             </DialogFooter>
         </DialogContent>
      </Dialog>

      <AlertDialog open={showDeleteDayDialog} onOpenChange={setShowDeleteDayDialog}>
          <AlertDialogContent>
              <AlertDialogHeader>
                  <AlertDialogTitle>Weet u het zeker?</AlertDialogTitle>
                  <AlertDialogDescription>
                      Deze actie kan niet ongedaan worden gemaakt. Dit verwijdert de productiedata voor {dayToDelete?.date ? format(new Date(dayToDelete.date + 'T00:00:00'), 'dd-MM-yyyy', { locale: nl }) : 'de geselecteerde dag'}.
                  </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                  <AlertDialogCancel>Annuleren</AlertDialogCancel>
                  <AlertDialogAction onClick={handleConfirmDeleteDay} className="bg-red-600 hover:bg-red-700">Verwijderen</AlertDialogAction>
              </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>

      {/* Hidden printable component */}
      <div style={{ display: 'none' }}>
        <div ref={componentRef}>
          <h1>Print Rapport voor {selectedLine}</h1>
          {/* Map over filteredRows etc. */}
        </div>
      </div>

    </div>
  );
};

export default ReportPage;