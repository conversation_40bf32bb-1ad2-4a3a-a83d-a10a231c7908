import React, { useState, useEffect, useMemo } from 'react';
import { useProduction } from '@/context/ProductionContext';
import { AlertCircle, PlusCircle, Trash2, Edit, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Incident } from '@/types/index';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format, parse } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Plus } from 'lucide-react';

const SafetyQualityPage: React.FC = () => {
  const {
    incidents,
    addIncident,
    updateIncident,
    removeIncident,
    archiveAndClearIncidents,
    isFlyLocked
  } = useProduction();
  
  const [safetyDialogOpen, setSafetyDialogOpen] = useState(false);
  const [qualityDialogOpen, setQualityDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentDate, setCurrentDate] = useState<Date | undefined>(undefined);
  const [editingIncidentId, setEditingIncidentId] = useState<string | null>(null);
  
  const safetyIncidents = useMemo(() => incidents.filter(inc => inc.type === 'safety'), [incidents]);
  const qualityIncidents = useMemo(() => incidents.filter(inc => inc.type === 'quality'), [incidents]);
  
  const hasSafetyIssues = safetyIncidents.length > 0 || 
                          incidents.some(i => i.type === 'safety' && (i.ltiReported || i.accidentReported));
  
  const hasQualityIssues = qualityIncidents.length > 0 || 
                           incidents.some(i => i.type === 'quality' && 
                           (i.ivOutOfSpec || i.korrelOutOfSpec || i.machineOutOfSpec));
  
  const [newIncident, setNewIncident] = useState<Omit<Incident, 'id' | 'createdAt'> & { id?: string, createdAt?: string }>({ 
      type: 'safety', 
      description: '', 
      location: '', 
      solution: '', 
      reported: 'nee', 
      followup: '', 
      responsible: '', 
      resolveDate: '', 
      ltiReported: false, 
      accidentReported: false, 
      ivOutOfSpec: false, 
      korrelOutOfSpec: false, 
      machineOutOfSpec: false 
  });
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const incidentDataFromForm = {
      ...newIncident,
      resolveDate: currentDate ? format(currentDate, 'yyyy-MM-dd') : (newIncident.resolveDate || new Date().toISOString().split('T')[0]),
    };

    try {
      if (editMode && editingIncidentId) {
        const originalIncident = incidents.find(inc => inc.id === editingIncidentId);
        const completeIncident: Incident = { 
          ...incidentDataFromForm,
          id: editingIncidentId,
          createdAt: originalIncident?.createdAt || new Date().toISOString(),
          type: incidentDataFromForm.type || (safetyDialogOpen ? 'safety' : 'quality')
        };
        console.log("Attempting to update incident with ID:", editingIncidentId);
        console.log("Data being sent to updateIncident:", completeIncident);
        updateIncident(completeIncident);
      } else {
        const incidentToAdd: Omit<Incident, 'id' | 'createdAt'> = {
          ...incidentDataFromForm,
          type: safetyDialogOpen ? 'safety' : 'quality'
        };
        delete (incidentToAdd as any).id;
        delete (incidentToAdd as any).createdAt;

        console.log("Attempting to add new incident:");
        console.log("Data being sent to addIncident:", incidentToAdd);
        addIncident(incidentToAdd);
      }
      resetForm();
      closeBothDialogs();
    } catch (error) {
       console.error("Error saving incident locally:", error);
       toast.error("Fout bij opslaan incident: " + (error instanceof Error ? error.message : String(error)));
    }
  };
  
  const resetForm = () => {
    setNewIncident({
      type: safetyDialogOpen ? 'safety' : 'quality',
      description: '',
      location: '',
      solution: '',
      reported: 'nee',
      followup: '',
      responsible: '',
      resolveDate: '',
      ltiReported: false,
      accidentReported: false,
      ivOutOfSpec: false,
      korrelOutOfSpec: false,
      machineOutOfSpec: false,
    });
    setCurrentDate(undefined);
    setEditMode(false);
    setEditingIncidentId(null);
  };
  
  const closeBothDialogs = () => {
    setSafetyDialogOpen(false);
    setQualityDialogOpen(false);
  };
  
  const handleInputChange = (field: keyof Omit<Incident, 'id'>, value: string | boolean) => {
    setNewIncident(prev => ({
      ...prev,
      [field]: value
    } as Omit<Incident, 'id'>));
  };

  const handleEdit = (incident: Incident) => {
    setNewIncident(incident);
    setEditingIncidentId(incident.id);
    setCurrentDate(incident.resolveDate ? parse(incident.resolveDate, 'yyyy-MM-dd', new Date()) : undefined);
    setEditMode(true);
    if (incident.type === 'safety') {
      setSafetyDialogOpen(true);
      setQualityDialogOpen(false);
    } else {
      setQualityDialogOpen(true);
      setSafetyDialogOpen(false);
    }
  };
  
  const handleDelete = async (id: string, type: 'safety' | 'quality') => {
    try {
      removeIncident(id, type, true);
      toast.success("Melding verwijderd en gearchiveerd.");
    } catch (error) {
       console.error("Error removing incident locally:", error);
       toast.error("Fout bij het lokaal verwijderen van de melding.");
    }
  };
  
  const handleOpenDialog = (incident?: Incident) => {
    if (incident) {
      handleEdit(incident);
    } else {
      resetForm();
      if (safetyDialogOpen) {
        setSafetyDialogOpen(true);
      } else {
        setQualityDialogOpen(true);
      }
    }
  };
  
  const handleCloseDialog = () => {
    closeBothDialogs();
    resetForm();
  };
  
  const filteredIncidents = editMode ? [newIncident] : (safetyDialogOpen ? safetyIncidents : qualityIncidents);
  
  return (
    <div className="animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-faerch-blue mb-4 md:mb-0">
          Veiligheid & Kwaliteit
        </h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="dashboard-card p-5">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Veiligheid</h2>
            <Dialog open={safetyDialogOpen} onOpenChange={(open) => {
              setSafetyDialogOpen(open);
              if (open) {
                 setNewIncident(prev => ({ ...prev, type: 'safety' }));
                 setEditMode(false);
                 setEditingIncidentId(null);
              }
              if (!open && !qualityDialogOpen) resetForm(); 
            }}>
              <DialogTrigger asChild>
                <Button variant="ghost" className="btn-nav-style" disabled={isFlyLocked}>
                  <span><PlusCircle className="w-4 h-4 mr-2 inline" />Nieuwe Veiligheidsmelding</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="md:max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{editMode ? 'Veiligheidsmelding bewerken' : 'Nieuwe veiligheidsmelding toevoegen'}</DialogTitle>
                  <DialogDescription>
                    Voer hier de details in voor de veiligheidsmelding.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="incident-location">Locatie</Label>
                    <Input 
                      id="incident-location" 
                      placeholder="Locatie van het incident"
                      value={newIncident.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="incident-description">Beschrijving</Label>
                    <Textarea 
                      id="incident-description" 
                      placeholder="Beschrijf het incident"
                      value={newIncident.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      required
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="incident-solution">Oplossing</Label>
                      <Textarea 
                        id="incident-solution" 
                        placeholder="Beschrijf de oplossing"
                        value={newIncident.solution}
                        onChange={(e) => handleInputChange('solution', e.target.value)}
                        rows={2}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="incident-followup">Opvolging</Label>
                      <Textarea 
                        id="incident-followup" 
                        placeholder="Beschrijf de opvolging"
                        value={newIncident.followup}
                        onChange={(e) => handleInputChange('followup', e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="incident-reported">Gerapporteerd</Label>
                      <Select 
                        value={newIncident.reported} 
                        onValueChange={(value) => handleInputChange('reported', value as 'ja' | 'nee')}
                      >
                        <SelectTrigger id="incident-reported">
                          <SelectValue placeholder="Gerapporteerd?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ja">Ja</SelectItem>
                          <SelectItem value="nee">Nee</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="incident-responsible">Verantwoordelijke</Label>
                      <Input 
                        id="incident-responsible" 
                        placeholder="Naam verantwoordelijke"
                        value={newIncident.responsible}
                        onChange={(e) => handleInputChange('responsible', e.target.value)}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="incident-resolve-date">Resolutiedatum</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !currentDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {currentDate ? format(currentDate, 'dd-MM-yyyy') : <span>Selecteer een datum</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={currentDate}
                          onSelect={setCurrentDate}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div className="space-y-4 bg-blue-50 p-4 rounded-md">
                    <h3 className="font-medium text-faerch-blue">Veiligheidsspecifieke informatie</h3>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="lti-reported"
                          checked={newIncident.ltiReported}
                          onCheckedChange={(checked) => 
                            handleInputChange('ltiReported', checked === true)
                          }
                        />
                        <Label htmlFor="lti-reported">LTI gerapporteerd</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="accident-reported"
                          checked={newIncident.accidentReported}
                          onCheckedChange={(checked) => 
                            handleInputChange('accidentReported', checked === true)
                          }
                        />
                        <Label htmlFor="accident-reported">Ongeval gerapporteerd</Label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button type="button" variant="ghost" onClick={() => {
                      setSafetyDialogOpen(false);
                      if (!qualityDialogOpen) resetForm();
                    }} className="btn-nav-style" disabled={isFlyLocked}>
                      <span>Annuleren</span>
                    </Button>
                    <Button type="submit" variant="ghost" className="btn-nav-style" disabled={isFlyLocked}>
                      <span>{editMode ? 'Wijzigingen opslaan' : 'Incident opslaan'}</span>
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          
          <div className={`flex items-center gap-4 p-4 rounded-md mb-4 ${hasSafetyIssues ? 'bg-red-100' : 'bg-green-100'}`}>
            {hasSafetyIssues ? (
              <>
                <XCircle className="h-8 w-8 text-red-500" />
                <div>
                  <h3 className={`text-xl font-bold text-red-500`}>NOK</h3>
                  <p className="text-gray-700">Veiligheidsincidenten gedetecteerd</p>
                </div>
              </>
            ) : (
              <>
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className={`text-xl font-bold text-green-500`}>OK</h3>
                  <p className="text-gray-700">Veiligheidsincidenten</p>
                </div>
              </>
            )}
          </div>
          
          <div className="mt-4 space-y-3">
            {safetyIncidents.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-6 bg-white/50 rounded-lg">
                <AlertCircle className="w-8 h-8 text-green-500 mb-2" />
                <p>Geen actieve veiligheidsincidenten</p>
              </div>
            ) : (
              <div className="space-y-4">
                {safetyIncidents.map(incident => (
                  <div key={incident.id} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                    <div className="bg-faerch-blue/10 px-4 py-3 flex justify-between items-center gap-4">
                      <h3 className="font-medium flex-1 min-w-0 break-words">{incident.description}</h3>
                      <div className="flex space-x-2 shrink-0">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleEdit(incident)}
                          className="btn-nav-style h-8 w-8 hover:text-blue-500"
                          disabled={isFlyLocked}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleDelete(incident.id, 'safety')}
                          className="btn-nav-style h-8 w-8 hover:text-red-500"
                          disabled={isFlyLocked}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4 space-y-3">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <p className="text-faerch-gray">Locatie:</p>
                          <p className="font-medium">{incident.location}</p>
                        </div>
                        <div>
                          <p className="text-faerch-gray">Verantwoordelijke:</p>
                          <p className="font-medium">{incident.responsible || "Niet toegewezen"}</p>
                        </div>
                      </div>
                      
                      <div className="pt-2 border-t border-gray-100">
                        <p className="text-faerch-gray text-sm">Oplossing:</p>
                        <p className="break-words">{incident.solution || "Geen oplossing gedefinieerd"}</p>
                      </div>

                      <div className="pt-2 border-t border-gray-100">
                        <p className="text-faerch-gray text-sm">Opvolging:</p>
                        <p className="break-words">{incident.followup || "Geen opvolging gedefinieerd"}</p>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 pt-2">
                        <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          Gerapporteerd: {incident.reported}
                        </div>
                        {incident.ltiReported && (
                          <div className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                            LTI gerapporteerd
                          </div>
                        )}
                        {incident.accidentReported && (
                          <div className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded">
                            Ongeval gerapporteerd
                          </div>
                        )}
                        <div className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                          Resolutiedatum: {incident.resolveDate}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className="dashboard-card p-5">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Kwaliteit</h2>
            <Dialog open={qualityDialogOpen} onOpenChange={(open) => {
              setQualityDialogOpen(open);
              if (open) {
                 setNewIncident(prev => ({ ...prev, type: 'quality' }));
                 setEditMode(false);
                 setEditingIncidentId(null);
              }
              if (!open && !safetyDialogOpen) resetForm();
            }}>
              <DialogTrigger asChild>
                <Button variant="ghost" className="btn-nav-style" disabled={isFlyLocked}>
                  <span><PlusCircle className="w-4 h-4 mr-2 inline" />Nieuwe Kwaliteitsmelding</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="md:max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{editMode ? 'Kwaliteitsmelding bewerken' : 'Nieuwe kwaliteitsmelding toevoegen'}</DialogTitle>
                  <DialogDescription>
                     Voer hier de details in voor de kwaliteitsmelding.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="incident-location">Locatie</Label>
                    <Input 
                      id="incident-location" 
                      placeholder="Locatie van het incident"
                      value={newIncident.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="incident-description">Beschrijving</Label>
                    <Textarea 
                      id="incident-description" 
                      placeholder="Beschrijf het incident"
                      value={newIncident.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      required
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="incident-solution">Oplossing</Label>
                      <Textarea 
                        id="incident-solution" 
                        placeholder="Beschrijf de oplossing"
                        value={newIncident.solution}
                        onChange={(e) => handleInputChange('solution', e.target.value)}
                        rows={2}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="incident-followup">Opvolging</Label>
                      <Textarea 
                        id="incident-followup" 
                        placeholder="Beschrijf de opvolging"
                        value={newIncident.followup}
                        onChange={(e) => handleInputChange('followup', e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="incident-reported">Gerapporteerd</Label>
                      <Select 
                        value={newIncident.reported} 
                        onValueChange={(value) => handleInputChange('reported', value as 'ja' | 'nee')}
                      >
                        <SelectTrigger id="incident-reported">
                          <SelectValue placeholder="Gerapporteerd?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ja">Ja</SelectItem>
                          <SelectItem value="nee">Nee</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="incident-responsible">Verantwoordelijke</Label>
                      <Input 
                        id="incident-responsible" 
                        placeholder="Naam verantwoordelijke"
                        value={newIncident.responsible}
                        onChange={(e) => handleInputChange('responsible', e.target.value)}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="incident-resolve-date">Resolutiedatum</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !currentDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {currentDate ? format(currentDate, 'dd-MM-yyyy') : <span>Selecteer een datum</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={currentDate}
                          onSelect={setCurrentDate}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div className="space-y-4 bg-faerch-green/10 p-4 rounded-md">
                    <h3 className="font-medium text-faerch-green">Kwaliteitsspecifieke informatie</h3>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="iv-out-of-spec"
                          checked={newIncident.ivOutOfSpec}
                          onCheckedChange={(checked) => 
                            handleInputChange('ivOutOfSpec', checked === true)
                          }
                        />
                        <Label htmlFor="iv-out-of-spec">IV buiten specificatie</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="korrel-out-of-spec"
                          checked={newIncident.korrelOutOfSpec}
                          onCheckedChange={(checked) => 
                            handleInputChange('korrelOutOfSpec', checked === true)
                          }
                        />
                        <Label htmlFor="korrel-out-of-spec">Korrel buiten specificatie</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="machine-out-of-spec"
                          checked={newIncident.machineOutOfSpec}
                          onCheckedChange={(checked) => 
                            handleInputChange('machineOutOfSpec', checked === true)
                          }
                        />
                        <Label htmlFor="machine-out-of-spec">Machine buiten specificatie</Label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button type="button" variant="ghost" onClick={() => {
                      setQualityDialogOpen(false);
                      if (!safetyDialogOpen) resetForm();
                    }} className="btn-nav-style" disabled={isFlyLocked}>
                      <span>Annuleren</span>
                    </Button>
                    <Button type="submit" variant="ghost" className="btn-nav-style" disabled={isFlyLocked}>
                      <span>{editMode ? 'Wijzigingen opslaan' : 'Incident opslaan'}</span>
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          
          <div className={`flex items-center gap-4 p-4 rounded-md mb-4 ${hasQualityIssues ? 'bg-red-100' : 'bg-green-100'}`}>
            {hasQualityIssues ? (
              <>
                <XCircle className="h-8 w-8 text-red-500" />
                <div>
                  <h3 className={`text-xl font-bold text-red-500`}>NOK</h3>
                  <p className="text-gray-700">Kwaliteitsincidenten gedetecteerd</p>
                </div>
              </>
            ) : (
              <>
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className={`text-xl font-bold text-green-500`}>OK</h3>
                  <p className="text-gray-700">Kwaliteitsincidenten</p>
                </div>
              </>
            )}
          </div>
          
          <div className="mt-4 space-y-3">
            {qualityIncidents.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-6 bg-white/50 rounded-lg">
                <AlertCircle className="w-8 h-8 text-green-500 mb-2" />
                <p>Geen actieve kwaliteitsincidenten</p>
              </div>
            ) : (
              <div className="space-y-4">
                {qualityIncidents.map(incident => (
                  <div key={incident.id} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                    <div className="bg-faerch-green/10 px-4 py-3 flex justify-between items-center gap-4">
                      <h3 className="font-medium flex-1 min-w-0 break-words">{incident.description}</h3>
                      <div className="flex space-x-2 shrink-0">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleEdit(incident)}
                          className="btn-nav-style h-8 w-8 hover:text-blue-500"
                          disabled={isFlyLocked}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleDelete(incident.id, 'quality')}
                          className="btn-nav-style h-8 w-8 hover:text-red-500"
                          disabled={isFlyLocked}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4 space-y-3">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <p className="text-faerch-gray">Locatie:</p>
                          <p className="font-medium">{incident.location}</p>
                        </div>
                        <div>
                          <p className="text-faerch-gray">Verantwoordelijke:</p>
                          <p className="font-medium">{incident.responsible || "Niet toegewezen"}</p>
                        </div>
                      </div>
                      
                      <div className="pt-2 border-t border-gray-100">
                        <p className="text-faerch-gray text-sm">Oplossing:</p>
                        <p className="break-words">{incident.solution || "Geen oplossing gedefinieerd"}</p>
                      </div>

                      <div className="pt-2 border-t border-gray-100">
                        <p className="text-faerch-gray text-sm">Opvolging:</p>
                        <p className="break-words">{incident.followup || "Geen opvolging gedefinieerd"}</p>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 pt-2">
                        <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          Gerapporteerd: {incident.reported}
                        </div>
                        {incident.ltiReported && (
                          <div className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                            LTI gerapporteerd
                          </div>
                        )}
                        {incident.accidentReported && (
                          <div className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded">
                            Ongeval gerapporteerd
                          </div>
                        )}
                        <div className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                          Resolutiedatum: {incident.resolveDate}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SafetyQualityPage;
