import React, { useState, useMemo } from 'react';
import { useProduction } from '@/context/ProductionContext';
// ... imports ...
import { Calendar } from "@/components/ui/calendar";
import { format, parseISO, isWithinInterval, startOfDay, endOfDay, subDays } from 'date-fns';
import { nl } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  Tooltip, 
  Legend, 
  CartesianGrid
} from 'recharts';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from "@/components/ui/accordion";
import { ProductionLine } from '@/types'; // Import ProductionLine type
import { Info } from 'lucide-react'; // Import Info icon for tooltip
import {
  Tooltip as ShadTooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip" // Import shadcn Tooltip
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { DateRange } from "react-day-picker";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from 'lucide-react'; 

// Nieuwe interface voor resultaten per lijn
interface LineSpecificResult {
  line: ProductionLine;
  material: string;
  totalProduction: number;
  totalYieldSum: number;
  yieldCount: number;
  totalTargetProduction: number;
  averageYield: number;
  efficiencyValue: number;
}

// Aangepaste ComparisonResult, kan nu meerdere lijnen bevatten
interface ComparisonResult {
  material: string;
  lineResults: LineSpecificResult[];
  // Eventueel geaggregeerde totalen als we die nog nodig hebben
  // aggregatedProduction: number; 
  // aggregatedAvgYield: number;
  // aggregatedEfficiency: number;
}

// ... type StatisticType ...
type StatisticType = 'production' | 'yield' | 'efficiency';

// Definieer P-lijnen type
type PLine = 'p1' | 'p2' | 'p3';
const pLines: PLine[] = ['p1', 'p2', 'p3'];

export const MaterialComparison: React.FC = () => {
  const { historyData, TARGETS } = useProduction();
  
  // ... state ...
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
  const [statisticType, setStatisticType] = useState<StatisticType>('production');
  // State voor P-lijn selectie (standaard leeg)
  const [selectedPLines, setSelectedPLines] = useState<PLine[]>([]); 
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 29),
    to: new Date(),
  });

  const categorizedMaterials = useMemo(() => {
    console.log("--- Start categoriseren materialen ---"); 
    const sMaterials = new Set<string>();
    const tMaterials = new Set<string>();

    historyData.forEach(entry => {
      Object.values(entry.data).forEach(lineData => {
        lineData.rows.forEach(row => {
          [row.od.material, row.md.material, row.nd.material].forEach(mat => {
            if (mat) { 
              console.log("Gevonden materiaal:", JSON.stringify(mat)); 
              const materialName = mat; 
              // Gebruik RegEx: spatie gevolgd door S of T aan het einde, case-insensitive
              if (/\sS$/i.test(materialName)) { 
                console.log(` -> Geclassificeerd als S: ${materialName}`); 
                sMaterials.add(materialName);
              } else if (/\sT$/i.test(materialName)) { 
                console.log(` -> Geclassificeerd als T: ${materialName}`); 
                tMaterials.add(materialName);
              } else {
                 console.log(` -> Niet geclassificeerd als S of T: ${materialName}`);
              }
            }
          });
        });
      });
    });
     console.log("--- Einde categoriseren materialen ---"); 
     console.log("S-Materialen:", Array.from(sMaterials)); 
     console.log("T-Materialen:", Array.from(tMaterials)); 

    return {
      sMaterials: Array.from(sMaterials).sort(),
      tMaterials: Array.from(tMaterials).sort(),
    };
  }, [historyData]);

  // Handler voor P-lijn selectie
  const handlePLineSelect = (line: PLine, checked: boolean | string) => {
    setSelectedPLines(prev =>
      checked
        ? [...prev, line]
        : prev.filter(l => l !== line)
    );
  };

  // ... handleMaterialSelect ...
  const handleMaterialSelect = (material: string, checked: boolean | string) => {
    setSelectedMaterials(prev => 
      checked 
        ? [...prev, material] 
        : prev.filter(m => m !== material)
    );
  };

  // ** HERZIENE BEREKENING met nieuwe Efficiency **
  const processedResults = useMemo((): LineSpecificResult[] => {
    if (selectedMaterials.length === 0) return [];

    // 1. Filter history op datum
    const filteredHistory = historyData.filter(entry => {
      if (!dateRange?.from) return true;
      const entryStart = startOfDay(parseISO(entry.startDate));
      const entryEnd = endOfDay(parseISO(entry.endDate));
      const rangeStart = startOfDay(dateRange.from);
      const rangeEnd = dateRange.to ? endOfDay(dateRange.to) : endOfDay(new Date());
      return (
        isWithinInterval(entryStart, { start: rangeStart, end: rangeEnd }) ||
        isWithinInterval(entryEnd, { start: rangeStart, end: rangeEnd }) ||
        (entryStart <= rangeStart && entryEnd >= rangeEnd)
      );
    });

    // 2. Verzamel data per materiaal per relevante lijn
    const lineResultsMap = new Map<string, LineSpecificResult>(); // Key: "materiaal-lijn"

    // Definieer de normen (kunnen later uit config/context komen)
    const MAX_PROD_NORM = 12000; 
    const MAX_YIELD_NORM = 57.5;

    for (const material of selectedMaterials) {
      const isSMaterial = categorizedMaterials.sMaterials.includes(material);
      const isTMaterial = categorizedMaterials.tMaterials.includes(material);

      // Bepaal relevante lijnen voor DIT materiaal
      const relevantLines: ProductionLine[] = [];
      if (isSMaterial) relevantLines.push('tl1');
      if (isTMaterial) relevantLines.push('tl2');
      relevantLines.push(...selectedPLines); // Voeg geselecteerde P-lijnen toe
      const uniqueRelevantLines = Array.from(new Set(relevantLines)); // Zorg voor unieke lijnen

      for (const line of uniqueRelevantLines) {
        let lineTotalProduction = 0;
        let lineTotalYieldSum = 0;
        let lineYieldCount = 0;
        let lineTotalTargetProduction = 0;
        const shiftEfficiencies: number[] = []; // Lijst voor shift efficiencies

        filteredHistory.forEach(entry => {
          // Kijk alleen naar data voor de huidige relevante lijn
          const lineData = entry.data[line];
          if (lineData) { 
             const dailyTarget = TARGETS[line] || 0;
             const shiftTarget = dailyTarget > 0 ? dailyTarget / 3 : 0;

             lineData.rows.forEach(row => {
              ([row.od, row.md, row.nd] as const).forEach(shift => {
                // Check of materiaal overeenkomt
                if (shift.material === material) {
                  // --- Start Logica binnen if --- 
                  const actualShiftProduction = Number(shift.production) || 0;
                  // Log voor aggregatie
                  console.log(
                    `Datum: ${row.date}, Lijn: ${line}, Materiaal: ${material}, ShiftProd: ${actualShiftProduction}, HuidigeTotaal: ${lineTotalProduction}`
                  );
                  lineTotalProduction += actualShiftProduction;
                  
                  if (shiftTarget > 0) {
                    lineTotalTargetProduction += shiftTarget;
                  }
                  const shiftYield = Number(shift.yield) || 0;
                  if (shiftYield > 0) {
                    lineTotalYieldSum += shiftYield;
                    lineYieldCount++;
                  }

                  // Bereken efficiency voor deze specifieke shift (alleen als er productie was)
                  if (actualShiftProduction > 0 && MAX_PROD_NORM > 0 && MAX_YIELD_NORM > 0) {
                    const prodComponent = Math.min(actualShiftProduction / MAX_PROD_NORM, 1); // Max 100%
                    const yieldComponent = Math.min(shiftYield / MAX_YIELD_NORM, 1); // Max 100%
                    const shiftEfficiency = Math.min(prodComponent, yieldComponent) * 100;
                    shiftEfficiencies.push(shiftEfficiency);
                  }
                  // --- Einde Logica binnen if --- 
                }
              });
            });
          }
        });

        // Sla resultaat op als er productie was op deze lijn voor dit materiaal
        if (lineTotalProduction > 0) {
            const averageYield = lineYieldCount > 0 ? lineTotalYieldSum / lineYieldCount : 0;
            
            // Bereken gemiddelde shift efficiency
            const averageShiftEfficiency = shiftEfficiencies.length > 0
              ? shiftEfficiencies.reduce((a, b) => a + b, 0) / shiftEfficiencies.length
              : 0;

            console.log(`Resultaat voor ${material} op ${line}: Prod=${lineTotalProduction}, TargetProd=${lineTotalTargetProduction}, AvgYield=${averageYield.toFixed(2)}%, AvgShiftEff=${averageShiftEfficiency.toFixed(2)}%`);
            const key = `${material}-${line}`;
            lineResultsMap.set(key, {
                line,
                material,
                totalProduction: lineTotalProduction,
                totalYieldSum: lineTotalYieldSum,
                yieldCount: lineYieldCount,
                totalTargetProduction: lineTotalTargetProduction,
                averageYield,
                efficiencyValue: averageShiftEfficiency, // Gebruik gemiddelde shift efficiency
            });
        }
      }
    }
    return Array.from(lineResultsMap.values());

  }, [selectedMaterials, dateRange, historyData, selectedPLines, TARGETS, categorizedMaterials]);

  // Filter de berekende resultaten voor weergave gebaseerd op selectie
  const finalResultsForDisplay = useMemo((): LineSpecificResult[] => {
      // Geen P-lijnen geselecteerd? Toon alleen TL1 voor S, TL2 voor T.
      if (selectedPLines.length === 0) {
          return processedResults.filter(res => 
              (categorizedMaterials.sMaterials.includes(res.material) && res.line === 'tl1') ||
              (categorizedMaterials.tMaterials.includes(res.material) && res.line === 'tl2')
          );
      }
      // Wel P-lijnen geselecteerd? Toon alleen resultaten voor die P-lijnen.
      else {
          return processedResults.filter(res => selectedPLines.includes(res.line as PLine));
      }
  }, [processedResults, selectedPLines, categorizedMaterials]);

  // Data voor de grafiek gebaseerd op finalResultsForDisplay
  const chartData = useMemo(() => {
    // Toon materiaal + lijn op X-as als P-lijnen geselecteerd zijn
    const useLineInName = selectedPLines.length > 0;
    return finalResultsForDisplay.map(result => ({
      name: useLineInName ? `${result.material} (${result.line.toUpperCase()})` : result.material,
      value: 
        statisticType === 'production' ? result.totalProduction :
        statisticType === 'yield' ? result.averageYield :
        result.efficiencyValue,
    }));
  }, [finalResultsForDisplay, statisticType, selectedPLines]);

  const formatYAxisTick = (value: number) => {
    if (statisticType === 'production') {
      return `${(value / 1000).toFixed(1)} t`;
    } else { 
      return `${value.toFixed(1)}%`;
    }
  };

  const formatTooltipValue = (value: number) => {
     if (statisticType === 'production') {
      return `${value.toLocaleString('nl-NL')} kg`;
    } else { 
      return `${value.toFixed(2).replace('.', ',')}%`;
    }
  };

  // ... MaterialCheckboxList helper (blijft hetzelfde) ...
   const MaterialCheckboxList = ({ title, materials }: { title: string, materials: string[] }) => (
    <AccordionItem value={title.toLowerCase().replace(' ', '-')}>
      <AccordionTrigger>{title} ({materials.length})</AccordionTrigger>
      <AccordionContent className="space-y-2 max-h-40 overflow-y-auto pr-2">
        {materials.length > 0 ? (
          materials.map((mat) => (
            <div key={mat} className="flex items-center space-x-2">
              <Checkbox 
                id={`mat-${mat}`}
                checked={selectedMaterials.includes(mat)}
                onCheckedChange={(checked) => handleMaterialSelect(mat, checked)}
              />
              <Label htmlFor={`mat-${mat}`} className="font-normal cursor-pointer text-sm">{mat}</Label>
            </div>
          ))
        ) : (
          <p className="text-xs text-gray-500 italic px-4">Geen materialen in deze categorie.</p>
        )}
      </AccordionContent>
    </AccordionItem>
  );

  return (
    <div className="animate-fade-in space-y-6">
      <h1 className="text-2xl font-semibold text-faerch-blue">Materiaal Vergelijking</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Kolom voor Selectie Opties */}
        <div className="lg:col-span-1 space-y-6">
          {/* ... Card: Selecteer Materialen (S/T Accordion) ... */}
           <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selecteer Materialen</CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="multiple" className="w-full">
                <MaterialCheckboxList title="S-Materialen" materials={categorizedMaterials.sMaterials} />
                <MaterialCheckboxList title="T-Materialen" materials={categorizedMaterials.tMaterials} />
              </Accordion>
              {(categorizedMaterials.sMaterials.length === 0 && 
                categorizedMaterials.tMaterials.length === 0) && (
                <p className="text-sm text-gray-500 italic pt-4">Geen S of T materialen gevonden in geschiedenis.</p>
              )}
            </CardContent>
          </Card>
          
          {/* Card: Selecteer P-Lijnen */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selecteer P-Lijnen</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {pLines.map((line) => (
                <div key={line} className="flex items-center space-x-2">
                  <Checkbox
                    id={`pline-${line}`}
                    checked={selectedPLines.includes(line)}
                    onCheckedChange={(checked) => handlePLineSelect(line, checked)}
                  />
                  <Label htmlFor={`pline-${line}`} className="font-normal cursor-pointer">
                    {line.toUpperCase()}
                  </Label>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selecteer Statistiek</CardTitle>
            </CardHeader>
            <CardContent>
              <RadioGroup 
                value={statisticType} 
                onValueChange={(value) => setStatisticType(value as StatisticType)}
                className="space-y-2"
              >
                {/* ... radio buttons (Production, Yield, Efficiency) ... */}
                 <div className="flex items-center space-x-2">
                  <RadioGroupItem value="production" id="stat-prod" />
                  <Label htmlFor="stat-prod" className="font-normal cursor-pointer">Totale Productie</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yield" id="stat-yield" />
                  <Label htmlFor="stat-yield" className="font-normal cursor-pointer">Yield (%)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="efficiency" id="stat-eff" />
                  <Label htmlFor="stat-eff" className="font-normal cursor-pointer flex items-center">
                    Efficiëntie (%)
                    <TooltipProvider delayDuration={100}>
                      <ShadTooltip>
                        <TooltipTrigger asChild>
                           <Info className="h-3 w-3 ml-1.5 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent side="top" className="max-w-xs">
                          <p className="text-xs">
                            Gemiddelde van shift efficiënties. Per shift: min(Prod / 12000, Yield / 57.5).
                          </p>
                        </TooltipContent>
                      </ShadTooltip>
                    </TooltipProvider>
                  </Label> 
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* ... Card: Selecteer Periode ... */}
           <Card>
             <CardHeader>
                <CardTitle className="text-lg">Selecteer Periode</CardTitle>
              </CardHeader>
              <CardContent>
                {/* ... Popover/Calendar ... */}
                 <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {
                        dateRange?.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "d MMM yyyy", { locale: nl })} - {" "}
                              {format(dateRange.to, "d MMM yyyy", { locale: nl })}
                            </>
                          ) : (
                            format(dateRange.from, "d MMM yyyy", { locale: nl })
                          )
                        ) : (
                          <span>Kies een periode</span>
                        )
                      }
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                      locale={nl}
                    />
                  </PopoverContent>
                </Popover>
              </CardContent>
           </Card>
        </div>

        {/* ... Kolom voor Grafiek en Tabel ... */}
         <div className="lg:col-span-2 space-y-6">
           {/* ... Card: Grafiek ... */}
            <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Vergelijkingsgrafiek (
                  {statisticType === 'production' ? 'Totale Productie' :
                  statisticType === 'yield' ? 'Yield (%)' :
                  'Efficiëntie (%)'}
                  {/* Toon geselecteerde P-lijnen in titel */}
                  {selectedPLines.length > 0 && selectedPLines.length < pLines.length && 
                    ` op ${selectedPLines.map(l => l.toUpperCase()).join(', ')}`
                  }
                  {selectedPLines.length === 0 && ` op Traylijnen`}
              )
              </CardTitle>
            </CardHeader>
            <CardContent>
              {chartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                   <BarChart data={chartData} margin={{ top: 5, right: 20, left: 10, bottom: 50 }}> {/* Meer bottom margin voor labels */}
                    <CartesianGrid strokeDasharray="3 3" />
                    {/* X-as toont nu materiaal (+ lijn indien P-lijn geselecteerd) */}
                    <XAxis dataKey="name" angle={-45} textAnchor="end" height={60} interval={0} fontSize={11} /> 
                    <YAxis tickFormatter={formatYAxisTick} width={50} fontSize={11} />
                    <Tooltip formatter={formatTooltipValue} />
                    <Bar 
                      dataKey="value" 
                      fill="#005587" 
                      name={
                        statisticType === 'production' ? 'Productie (kg)' :
                        statisticType === 'yield' ? 'Yield (%)' :
                        'Efficiëntie (%)'
                      } 
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <p className="text-center text-gray-500 py-10">
                  {selectedMaterials.length === 0 
                    ? "Selecteer minimaal één materiaal." 
                    : "Geen data gevonden voor de geselecteerde materialen, lijnen en periode."
                  }
                </p>
              )}
            </CardContent>
          </Card>
          {/* ... Card: Tabel ... */}
           {finalResultsForDisplay.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Resultaten Overzicht</CardTitle>
              </CardHeader>
              <CardContent>
                 <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Materiaal</TableHead>
                      {/* Toon Lijn kolom alleen als P-lijnen zijn geselecteerd */}
                      {selectedPLines.length > 0 && <TableHead>Lijn</TableHead>}
                      <TableHead className="text-right">Totale Productie (kg)</TableHead>
                      <TableHead className="text-right">Yield (%)</TableHead>
                      <TableHead className="text-right">Efficiëntie (%)</TableHead> 
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {finalResultsForDisplay.map((result, index) => (
                      // Gebruik unieke key incl. lijn
                      <TableRow key={`${result.material}-${result.line}-${index}`}> 
                        <TableCell className="font-medium">{result.material}</TableCell>
                        {selectedPLines.length > 0 && <TableCell>{result.line.toUpperCase()}</TableCell>}
                        <TableCell className="text-right">{result.totalProduction.toLocaleString('nl-NL')}</TableCell>
                        <TableCell className="text-right">{result.averageYield.toFixed(2).replace('.', ',')}</TableCell>
                        <TableCell className="text-right">{result.efficiencyValue.toFixed(2).replace('.', ',')}</TableCell> 
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};