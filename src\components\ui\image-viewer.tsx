import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut } from 'lucide-react';
import { But<PERSON> } from './button';
import { Dialog, DialogContent, DialogTitle } from './dialog';
import { VisuallyHidden } from './visually-hidden';

interface ImageViewerProps {
  images: string[];
  initialIndex?: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ImageViewer: React.FC<ImageViewerProps> = ({
  images,
  initialIndex = 0,
  open,
  onOpenChange,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [zoom, setZoom] = useState(1);
  const [loading, setLoading] = useState(true);

  // Reset zoom and loading state when image changes or dialog opens
  useEffect(() => {
    setZoom(1);
    setLoading(true);
    // Reset to initial index when dialog opens
    if (open) {
      setCurrentIndex(initialIndex);
    }
  }, [open, initialIndex]);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.5, 3)); // Max zoom 3x
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.5, 0.5)); // Min zoom 0.5x
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      handlePrevious();
    } else if (e.key === 'ArrowRight') {
      handleNext();
    } else if (e.key === 'Escape') {
      onOpenChange(false);
    } else if (e.key === '+' || e.key === '=') {
      handleZoomIn();
    } else if (e.key === '-') {
      handleZoomOut();
    }
  };

  const handleImageLoad = () => {
    setLoading(false);
  };

  if (images.length === 0) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="w-screen h-screen max-w-none max-h-none p-0 bg-black/90 border-none overflow-hidden"
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        <DialogTitle className="sr-only">
          <VisuallyHidden>Afbeelding {currentIndex + 1} van {images.length}</VisuallyHidden>
        </DialogTitle>

        <div className="relative flex flex-col items-center justify-center h-full w-full">
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 text-white hover:bg-white/20 z-20"
            onClick={() => onOpenChange(false)}
            aria-label="Sluiten"
          >
            <X className="h-6 w-6" />
          </Button>

          {/* Zoom controls */}
          <div className="absolute top-2 left-2 flex gap-2 z-20">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={handleZoomOut}
              aria-label="Uitzoomen"
            >
              <ZoomOut className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={handleZoomIn}
              aria-label="Inzoomen"
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
          </div>

          {/* Image container */}
          <div className="flex-1 flex items-center justify-center w-full h-full overflow-auto">
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <img
              src={images[currentIndex]}
              alt={`Afbeelding ${currentIndex + 1}`}
              className="object-contain transition-transform duration-200"
              style={{
                transform: `scale(${zoom})`,
                maxHeight: loading ? '0' : '95vh',
                maxWidth: loading ? '0' : '95vw',
              }}
              onLoad={handleImageLoad}
            />
          </div>

          {/* Navigation buttons */}
          {images.length > 1 && (
            <div className="absolute inset-x-0 bottom-0 flex justify-between p-4 z-20">
              <Button
                variant="ghost"
                size="icon"
                className="text-white hover:bg-white/20"
                onClick={handlePrevious}
                aria-label="Vorige afbeelding"
              >
                <ChevronLeft className="h-8 w-8" />
              </Button>

              <div className="text-white text-sm bg-black/50 px-2 py-1 rounded-md">
                {currentIndex + 1} / {images.length}
              </div>

              <Button
                variant="ghost"
                size="icon"
                className="text-white hover:bg-white/20"
                onClick={handleNext}
                aria-label="Volgende afbeelding"
              >
                <ChevronRight className="h-8 w-8" />
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
