import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import {
  ProductionData,
  ProductionLine,
  Disruption,
  DisruptionInput,
  HistoryEntry,
  Incident,
  LineData,
  ProductionRow,
  EquipmentEntry,
  EquipmentOptions,
  DowntimeReason,
  LogbookEntry,
  LogbookPriority,
  RcaEntry,
  ProductionShiftData,
  TdLogEntry, TdLogEntryInput
} from '@/types';
import {
  defaultProductionData, // Use the updated default
  ProductionContextType
} from '@/types/production-context';
import { SHIFT_TARGETS, YIELD_TARGETS } from '@/config/targets';
import { loadItem, saveItem, removeItem } from '@/lib/local-storage';
import {
  createEmptyProductionRow,
  addEquipmentOptionToData,
  removeEquipmentOptionFromData,
  editEquipmentOptionInData,
  addEquipmentAreaToData,
  removeEquipmentAreaFromData,
  editEquipmentAreaInData
} from '@/utils/production-context-utils';
import { getShiftInfo } from '@/utils/shift-utils';
import { useTransaction } from '@/context/TransactionContext';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '@/lib/supabase-client';
import { RealtimeChannel } from '@supabase/supabase-js';

// Define localStorage keys
const STORAGE_KEYS = {
  PRODUCTION_DATA: 'productionData',
  HISTORY_DATA: 'historyData',
  INCIDENTS_DATA: 'incidentsData',
  VK_HISTORY_DATA: 'vkHistoryData',
  DISRUPTION_HISTORY_DATA: 'disruptionHistoryData',
  EQUIPMENT_OPTIONS: 'equipmentOptionsData',
  PRODUCTION_TARGETS: 'productionTargets',
  PRODUCTION_YIELD_TARGETS: 'productionYieldTargets',
  DOWNTIME_REASONS: 'downtimeReasons',
  INDEPENDENT_DISRUPTIONS: 'independentDisruptionsData',
  LOGBOOK_ENTRIES: 'logbookEntriesData',
  LOGBOOK_HISTORY: 'logbookHistoryData',
  // No key needed for TD Logbook if using direct table access
};

// Helper function for default values including new lines
const getDefaultLineRecord = <T>(defaultValue: T): Record<ProductionLine, T> => ({
    tl1: defaultValue, tl2: defaultValue, p1: defaultValue, p2: defaultValue, p3: defaultValue,
    Yard: defaultValue, Koeling: defaultValue, Gebouw: defaultValue, Overige: defaultValue
});

const getDefaultDowntimeReasons = (): Record<ProductionLine, DowntimeReason[]> => getDefaultLineRecord([]);
const getDefaultIndependentDisruptions = (): Record<ProductionLine, (DisruptionInput | null)[]> => getDefaultLineRecord([null, null, null]);
const getDefaultLogbookEntries = (): LogbookEntry[] => ([]);
const getDefaultLogbookHistory = (): LogbookEntry[] => ([]);
const getDefaultEquipmentOptions = (): EquipmentOptions => getDefaultLineRecord({});
const getDefaultMaterialOptions = (): Record<ProductionLine, string[]> => ({
    ...getDefaultLineRecord([]), // Start with empty for all
    // Add specific defaults if needed, otherwise they remain empty
    tl1: ['Suezpmdx S', 'Prezpmdx S', 'Attewimi S', 'Eurokey S'],
    tl2: ['Suezpmdx T', 'Prezpmdx T', 'Attewimi T', 'Eurokey T'],
    p1: ['Suezpmdx S', 'Suezpmdx T', 'Eurokey T', 'Eurokey S'],
    p2: ['Suezpmdx S', 'Suezpmdx T', 'Eurokey T', 'Eurokey S'],
    p3: ['Suezpmdx S', 'Suezpmdx T', 'Eurokey T', 'Eurokey S'],
});


export const createInitialShiftData = (): ProductionShiftData => ({
  production: 0, // Initialize as number 0
  yield: 0,      // Initialize as number 0
  material: '',
  target: 0
});


export const useProductionState = (): ProductionContextType => {
  const { isInTransaction } = useTransaction();

  // --- State Definitions ---
  const [productionData, setProductionData] = useState<ProductionData>(() => loadItem<ProductionData>(STORAGE_KEYS.PRODUCTION_DATA, defaultProductionData) || defaultProductionData);
  const [historyData, setHistoryData] = useState<HistoryEntry[]>(() => loadItem<HistoryEntry[]>(STORAGE_KEYS.HISTORY_DATA, []) || []);
  const [incidents, setIncidents] = useState<Incident[]>(() => loadItem<Incident[]>(STORAGE_KEYS.INCIDENTS_DATA, []) || []);
  const [vkHistoryData, setVkHistoryData] = useState<Incident[]>(() => loadItem<Incident[]>(STORAGE_KEYS.VK_HISTORY_DATA, []) || []);
  const [disruptionHistory, setDisruptionHistory] = useState<Disruption[]>(() => loadItem<Disruption[]>(STORAGE_KEYS.DISRUPTION_HISTORY_DATA, []) || []);
  const [materialOptions, setMaterialOptions] = useState<Record<ProductionLine, string[]>>(() => {
    const savedData = loadItem<ProductionData>(STORAGE_KEYS.PRODUCTION_DATA, defaultProductionData);
    const defaultOptions = getDefaultMaterialOptions();
    if (savedData) {
        (Object.keys(defaultOptions) as ProductionLine[]).forEach(line => {
            // Ensure savedData[line] exists before accessing materials
            defaultOptions[line] = savedData[line]?.materials || defaultOptions[line];
        });
    }
    return defaultOptions;
  });
  const [equipmentOptions, setEquipmentOptions] = useState<EquipmentOptions>(() => loadItem<EquipmentOptions>(STORAGE_KEYS.EQUIPMENT_OPTIONS, getDefaultEquipmentOptions()) || getDefaultEquipmentOptions());
  const [targets, setTargets] = useState<Record<ProductionLine, number>>(() => loadItem<Record<ProductionLine, number>>(STORAGE_KEYS.PRODUCTION_TARGETS, SHIFT_TARGETS) || SHIFT_TARGETS);
  const [yieldTargets, setYieldTargets] = useState<Record<ProductionLine, number>>(() => loadItem<Record<ProductionLine, number>>(STORAGE_KEYS.PRODUCTION_YIELD_TARGETS, YIELD_TARGETS) || YIELD_TARGETS);
  const [downtimeReasons, setDowntimeReasons] = useState<Record<ProductionLine, DowntimeReason[]>>(() => loadItem<Record<ProductionLine, DowntimeReason[]>>(STORAGE_KEYS.DOWNTIME_REASONS, getDefaultDowntimeReasons()) || getDefaultDowntimeReasons());
  const [isFlyLocked, setIsFlyLocked] = useState<boolean>(false);
  const [independentDisruptions, setIndependentDisruptions] = useState<Record<ProductionLine, (DisruptionInput | null)[]>>(() => loadItem<Record<ProductionLine, (DisruptionInput | null)[]>>(STORAGE_KEYS.INDEPENDENT_DISRUPTIONS, getDefaultIndependentDisruptions()) || getDefaultIndependentDisruptions());
  const [logbookEntries, setLogbookEntries] = useState<LogbookEntry[]>(() => {
    const loaded = loadItem<LogbookEntry[] | Record<string, any>>(STORAGE_KEYS.LOGBOOK_ENTRIES, getDefaultLogbookEntries());
    return Array.isArray(loaded) ? loaded : getDefaultLogbookEntries();
  });
  const [logbookHistory, setLogbookHistory] = useState<LogbookEntry[]>(() => {
    const loaded = loadItem<LogbookEntry[] | Record<string, any>>(STORAGE_KEYS.LOGBOOK_HISTORY, getDefaultLogbookHistory());
    return Array.isArray(loaded) ? loaded : getDefaultLogbookHistory();
  });
  const [tdLogEntries, setTdLogEntries] = useState<TdLogEntry[]>([]);

  // --- Supabase Realtime Subscription (Generic Data) ---
  useEffect(() => {
    console.log('[useProductionState] Setting up Supabase Realtime subscription...');
    const handleUpsert = (payload: any, eventType: 'INSERT' | 'UPDATE') => {
      console.log(`[Realtime] ${eventType} received:`, payload);
      const { key, value } = payload.new;
      try {
        switch (key) {
          case STORAGE_KEYS.PRODUCTION_DATA:
            const newProdData = value as ProductionData; setProductionData(newProdData);
            setMaterialOptions(prevMatOpts => {
                const newMatOpts = { ...getDefaultMaterialOptions(), ...prevMatOpts }; // Ensure all keys exist
                (Object.keys(newMatOpts) as ProductionLine[]).forEach(line => {
                    newMatOpts[line] = newProdData[line]?.materials || newMatOpts[line] || [];
                });
                return newMatOpts;
            });
            break;
          case STORAGE_KEYS.HISTORY_DATA: setHistoryData(value as HistoryEntry[]); break;
          case STORAGE_KEYS.INCIDENTS_DATA: setIncidents(value as Incident[]); break;
          case STORAGE_KEYS.VK_HISTORY_DATA: setVkHistoryData(value as Incident[]); break;
          case STORAGE_KEYS.DISRUPTION_HISTORY_DATA:
            console.log('[Realtime] Received DISRUPTION_HISTORY_DATA update. Value:', value);
            setDisruptionHistory(prevHistory => {
              console.log('[Realtime] setDisruptionHistory - Previous state:', prevHistory);
              const newState = value as Disruption[];
              console.log('[Realtime] setDisruptionHistory - New state:', newState);
              return newState;
            });
            console.log('[Realtime] Finished processing DISRUPTION_HISTORY_DATA.');
            break;
          case STORAGE_KEYS.EQUIPMENT_OPTIONS: setEquipmentOptions(value as EquipmentOptions); break;
          case STORAGE_KEYS.PRODUCTION_TARGETS: setTargets(value as Record<ProductionLine, number>); break;
          case STORAGE_KEYS.PRODUCTION_YIELD_TARGETS: setYieldTargets(value as Record<ProductionLine, number>); break;
          case STORAGE_KEYS.DOWNTIME_REASONS: setDowntimeReasons(value as Record<ProductionLine, DowntimeReason[]>); break;
          case STORAGE_KEYS.INDEPENDENT_DISRUPTIONS: setIndependentDisruptions(value as Record<ProductionLine, (DisruptionInput | null)[]>); break;
          case STORAGE_KEYS.LOGBOOK_ENTRIES: setLogbookEntries(Array.isArray(value) ? value : getDefaultLogbookEntries()); break;
          case STORAGE_KEYS.LOGBOOK_HISTORY: setLogbookHistory(Array.isArray(value) ? value : getDefaultLogbookHistory()); break;
          default: console.warn(`[Realtime] Received ${eventType} for unhandled key: ${key}`);
        }
      } catch (parseError) { console.error(`[Realtime] Error processing ${eventType} for key ${key}:`, parseError, 'Payload value:', value); }
    };
    const handleInsert = (payload: any) => handleUpsert(payload, 'INSERT');
    const handleUpdate = (payload: any) => handleUpsert(payload, 'UPDATE');
    const handleDelete = (payload: any) => {
      console.log('[Realtime] Delete received:', payload); const { key } = payload.old;
      try {
         switch (key) {
          case STORAGE_KEYS.PRODUCTION_DATA: setProductionData(defaultProductionData); setMaterialOptions(getDefaultMaterialOptions()); break;
          case STORAGE_KEYS.HISTORY_DATA: setHistoryData([]); break;
          case STORAGE_KEYS.INCIDENTS_DATA: setIncidents([]); break;
          case STORAGE_KEYS.VK_HISTORY_DATA: setVkHistoryData([]); break;
          case STORAGE_KEYS.DISRUPTION_HISTORY_DATA: setDisruptionHistory([]); break;
          case STORAGE_KEYS.EQUIPMENT_OPTIONS: setEquipmentOptions(getDefaultEquipmentOptions()); break;
          case STORAGE_KEYS.PRODUCTION_TARGETS: setTargets(SHIFT_TARGETS); break;
          case STORAGE_KEYS.PRODUCTION_YIELD_TARGETS: setYieldTargets(YIELD_TARGETS); break;
          case STORAGE_KEYS.DOWNTIME_REASONS: setDowntimeReasons(getDefaultDowntimeReasons()); break;
          case STORAGE_KEYS.INDEPENDENT_DISRUPTIONS: setIndependentDisruptions(getDefaultIndependentDisruptions()); break;
          case STORAGE_KEYS.LOGBOOK_ENTRIES: setLogbookEntries(getDefaultLogbookEntries()); break;
          case STORAGE_KEYS.LOGBOOK_HISTORY: setLogbookHistory(getDefaultLogbookHistory()); break;
          default: console.warn(`[Realtime] Received DELETE for unhandled key: ${key}`);
        }
      } catch (error) { console.error(`[Realtime] Error processing DELETE for key ${key}:`, error, 'Payload:', payload); }
    };
    const channel: RealtimeChannel = supabase
      .channel('local_storage_data_changes')
      .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'local_storage_data' }, handleInsert)
      .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'local_storage_data' }, handleUpdate)
      .on('postgres_changes', { event: 'DELETE', schema: 'public', table: 'local_storage_data' }, handleDelete)
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') { console.log('[Realtime] Successfully subscribed!'); }
        else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') { console.error(`[Realtime] Subscription error: ${status}`, err); toast.error('Realtime connection error'); }
        else { console.log(`[Realtime] Subscription status: ${status}`); }
      });
    return () => { console.log('[useProductionState] Cleaning up Realtime subscription...'); supabase.removeChannel(channel); };
  }, []);

  // --- TD Logbook: Initial Fetch ---
  useEffect(() => {
    const fetchTdLogEntries = async () => {
      console.log('[TD Logbook] Fetching initial entries...');
      const { data, error } = await supabase
        .from('td_logbook_entries')
        .select('*')
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('[TD Logbook] Error fetching initial entries:', error);
        toast.error('Fout bij laden TD Logboek.');
      } else {
        console.log('[TD Logbook] Fetched entries:', data);
        setTdLogEntries(data || []);
      }
    };
    fetchTdLogEntries();
  }, []);

  // --- TD Logbook: Realtime Subscription ---
  useEffect(() => {
    console.log('[TD Logbook] Setting up Realtime subscription...');
    const channel = supabase
      .channel('td_logbook_entries_changes')
      .on<TdLogEntry>(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'td_logbook_entries' },
        (payload) => {
          console.log('[TD Logbook] Realtime change received:', payload);
          switch ((payload as any).eventType) {
            case 'INSERT':
              setTdLogEntries((prevEntries) =>
                [(payload as any).new, ...prevEntries].sort((a, b) =>
                  new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
                )
              );
              break;
            case 'UPDATE':
              setTdLogEntries((prevEntries) =>
                prevEntries.map((entry) =>
                  entry.id === (payload as any).new.id ? (payload as any).new : entry
                )
              );
              break;
            case 'DELETE':
              setTdLogEntries((prevEntries) =>
                prevEntries.filter((entry) => entry.id !== (payload as any).old.id)
              );
              break;
            default:
              console.warn('[TD Logbook] Unhandled Realtime event type:', (payload as any).eventType);
          }
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('[TD Logbook] Realtime successfully subscribed!');
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error(`[TD Logbook] Realtime subscription error: ${status}`, err);
          toast.error('TD Logboek Realtime connection error');
        } else {
          console.log(`[TD Logbook] Realtime subscription status: ${status}`);
        }
      });

    return () => {
      console.log('[TD Logbook] Cleaning up Realtime subscription...');
      supabase.removeChannel(channel);
    };
  }, []);

  // --- State Update Wrappers ---
  const updateAndSaveProductionData = useCallback((updater: (prev: ProductionData) => ProductionData) => { setProductionData(prev => { const next = updater(prev); if (!isInTransaction) saveItem(STORAGE_KEYS.PRODUCTION_DATA, next); return next; }); }, [isInTransaction]);
  const updateAndSaveHistoryData = useCallback((updater: (prev: HistoryEntry[]) => HistoryEntry[]) => { setHistoryData(prev => { const next = updater(prev); saveItem(STORAGE_KEYS.HISTORY_DATA, next); return next; }); }, []);
  const updateAndSaveIncidents = useCallback((updater: (prev: Incident[]) => Incident[]) => { setIncidents(prev => { const next = updater(prev); saveItem(STORAGE_KEYS.INCIDENTS_DATA, next); return next; }); }, []);
  const updateAndSaveVkHistoryData = useCallback((updater: (prev: Incident[]) => Incident[]) => { setVkHistoryData(prev => { const next = updater(prev); const unique = next.filter((i, idx, self) => idx === self.findIndex(t => t.id === i.id)); saveItem(STORAGE_KEYS.VK_HISTORY_DATA, unique); return unique; }); }, []);
  const updateAndSaveDisruptionHistory = useCallback((updater: (prev: Disruption[]) => Disruption[]) => {
    setDisruptionHistory(prev => {
      const next = updater(prev);
      next.sort((a, b) => (b.resolvedAt ? new Date(b.resolvedAt).getTime() : 0) - (a.resolvedAt ? new Date(a.resolvedAt).getTime() : 0));
      const unique = next.filter((i, idx, self) => idx === self.findIndex(t => t.id === i.id));
      saveItem(STORAGE_KEYS.DISRUPTION_HISTORY_DATA, unique);
      return unique;
    });
  }, []);
  const updateAndSaveMaterialOptions = useCallback((updater: (prev: Record<ProductionLine, string[]>) => Record<ProductionLine, string[]>) => { setMaterialOptions(updater); }, []);
  const updateAndSaveEquipmentOptions = useCallback((updater: (prev: EquipmentOptions) => EquipmentOptions) => { setEquipmentOptions(prev => { const next = updater(prev); saveItem(STORAGE_KEYS.EQUIPMENT_OPTIONS, next); return next; }); }, []);
  const updateAndSaveTargets = useCallback((newTargets: Record<ProductionLine, number>) => { setTargets(newTargets); saveItem(STORAGE_KEYS.PRODUCTION_TARGETS, newTargets); }, []);
  const updateAndSaveYieldTargets = useCallback((newYieldTargets: Record<ProductionLine, number>) => { setYieldTargets(newYieldTargets); saveItem(STORAGE_KEYS.PRODUCTION_YIELD_TARGETS, newYieldTargets); }, []);
  const updateAndSaveDowntimeReasons = useCallback((updater: (prev: Record<ProductionLine, DowntimeReason[]>) => Record<ProductionLine, DowntimeReason[]>) => { setDowntimeReasons(prev => { const next = updater(prev); saveItem(STORAGE_KEYS.DOWNTIME_REASONS, next); return next; }); }, []);
  const updateAndSaveIndependentDisruptions = useCallback((updater: (prev: Record<ProductionLine, (DisruptionInput | null)[]>) => Record<ProductionLine, (DisruptionInput | null)[]>) => { setIndependentDisruptions(prev => { const next = updater(prev); saveItem(STORAGE_KEYS.INDEPENDENT_DISRUPTIONS, next); return next; }); }, []);
  const updateAndSaveLogbookEntries = useCallback((updater: (prev: LogbookEntry[]) => LogbookEntry[]) => { setLogbookEntries(prev => { const next = updater(prev); next.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()); saveItem(STORAGE_KEYS.LOGBOOK_ENTRIES, next); return next; }); }, []);
  const updateAndSaveLogbookHistory = useCallback((updater: (prev: LogbookEntry[]) => LogbookEntry[]) => { setLogbookHistory(prev => { const next = updater(prev); next.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()); saveItem(STORAGE_KEYS.LOGBOOK_HISTORY, next); return next; }); }, []);

  // --- State Update Functions ---
  const addOrUpdateProductionRow = useCallback((line: ProductionLine, date: string, rowData: ProductionRow) => { updateAndSaveProductionData(prev => { const d = prev[line] || { rows: [], materials: [], disruptions: [], equipmentOptions: {} }; const idx = d.rows.findIndex(r => r.date === date); let rows; if (idx >= 0) rows = [...d.rows.slice(0, idx), rowData, ...d.rows.slice(idx + 1)]; else { rows = [...d.rows, rowData]; rows.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); } return { ...prev, [line]: { ...d, rows } }; }); }, [updateAndSaveProductionData]);
  const updateProductionRow = useCallback((line: ProductionLine, date: string, updatedRowData: Partial<ProductionRow>) => { updateAndSaveProductionData(prev => { const d = prev[line]; if (!d) return prev; const rows = d.rows.map(r => r.date === date ? { ...r, ...updatedRowData } : r); return { ...prev, [line]: { ...d, rows } }; }); }, [updateAndSaveProductionData]);
  const addProductionRow = useCallback((line: ProductionLine, date: string, initialData?: Partial<ProductionRow>) => {
    const t = targets[line] || 0;
    const m = materialOptions[line] || [];
    const od = initialData?.od || createInitialShiftData();
    const md = initialData?.md || createInitialShiftData();
    const nd = initialData?.nd || createInitialShiftData();
    const n = createEmptyProductionRow(line, date, t, m, { od, md, nd });
    addOrUpdateProductionRow(line, date, n);
  }, [addOrUpdateProductionRow, targets, materialOptions]);
  const removeProductionRow = useCallback((line: ProductionLine, date: string) => { updateAndSaveProductionData(prev => { const d = prev[line]; if (!d) return prev; const rows = d.rows.filter(r => r.date !== date); console.log(`Removing row ${date} for ${line}. New count: ${rows.length}`); return { ...prev, [line]: { ...d, rows } }; }); }, [updateAndSaveProductionData]);
  const clearProductionData = useCallback((line: ProductionLine) => { updateAndSaveProductionData(prev => ({ ...prev, [line]: { rows: [], materials: prev[line]?.materials || [], disruptions: [], equipmentOptions: prev[line]?.equipmentOptions || {} } })); removeItem(`production_${line}`); removeItem(`breakdowns_${line}`); toast.success(`Productiedata voor ${line.toUpperCase()} gewist`); }, [updateAndSaveProductionData]);
  const addToHistory = useCallback((entry: Omit<HistoryEntry, 'timestamp'>) => { updateAndSaveHistoryData(prev => { const n = { ...entry, timestamp: new Date().toISOString() }; const dup = prev.find(e => e.line === n.line && e.startDate === n.startDate && e.endDate === n.endDate && JSON.stringify(e.data) === JSON.stringify(n.data)); if (dup) { console.warn('Duplicate history entry prevented'); return prev; } return [...prev, n].sort((a, b) => new Date(b.endDate).getTime() - new Date(a.endDate).getTime()); }); }, [updateAndSaveHistoryData]);
  const resetData = useCallback(() => { const copy = JSON.parse(JSON.stringify(productionData)); let added = 0; Object.entries(copy).forEach(([l, d]) => { const lk = l as ProductionLine; const ld = d as LineData; if (ld.rows.length > 0) { const hld: LineData = { rows: [...ld.rows], disruptions: [], materials: ld.materials || [], equipmentOptions: ld.equipmentOptions || {} }; const dfh: Partial<ProductionData> = { [lk]: hld }; const dates = ld.rows.map(r => r.date).filter(Boolean); const sd = dates.length > 0 ? new Date(Math.min(...dates.map(dt => new Date(dt + 'T00:00:00').getTime()))).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]; const ed = dates.length > 0 ? new Date(Math.max(...dates.map(dt => new Date(dt + 'T00:00:00').getTime()))).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]; addToHistory({ startDate: sd, endDate: ed, data: dfh as ProductionData, line: lk }); added++; } }); updateAndSaveProductionData(prev => { const cd = { ...defaultProductionData }; return cd; }); if (added > 0) toast.success(`${added} lijn(en) gearchiveerd.`); else toast.info("Geen data te archiveren."); }, [updateAndSaveProductionData, productionData, addToHistory]);
  const updateLineData = useCallback((line: ProductionLine, data: LineData) => { updateAndSaveProductionData(prev => ({ ...prev, [line]: data })); }, [updateAndSaveProductionData]);
  const addMaterialOption = useCallback((line: ProductionLine, material: string) => { updateAndSaveProductionData(prev => { const cl = prev[line] || { rows: [], materials: [], disruptions: [], equipmentOptions: {} }; const nd = { ...prev, [line]: { ...cl, materials: [...(cl.materials || []), material] } }; setMaterialOptions(po => ({ ...po, [line]: nd[line].materials })); return nd; }); }, [updateAndSaveProductionData]);
  const removeMaterialOption = useCallback((line: ProductionLine, material: string) => { updateAndSaveProductionData(prev => { if (!prev[line]) return prev; const nd = { ...prev, [line]: { ...prev[line], materials: prev[line].materials.filter(m => m !== material) } }; setMaterialOptions(po => ({ ...po, [line]: nd[line].materials })); return nd; }); toast.success(`Materiaal ${material} verwijderd.`); }, [updateAndSaveProductionData]);
  const addDisruption = useCallback((line: ProductionLine, disruptionInput: DisruptionInput) => { updateAndSaveProductionData(prev => { const ld = prev[line] || { rows: [], materials: [], disruptions: [], equipmentOptions: {} }; const nd: Disruption = { ...disruptionInput, id: crypto.randomUUID(), line: line, createdAt: new Date().toISOString() }; const ud = [nd, ...(ld.disruptions || [])]; return { ...prev, [line]: { ...ld, disruptions: ud } }; }); toast.success("Lopende storing toegevoegd"); }, [updateAndSaveProductionData]);
  const updateDisruption = useCallback((line: ProductionLine, id: string, updatedData: Partial<Disruption>) => {
    updateAndSaveProductionData(prev => {
      const ld = prev[line];
      if (!ld?.disruptions) return prev;
      const ud = ld.disruptions.map(d => {
        if (d.id === id) {
          const newRcaArray = updatedData.rootCauseAnalysis !== undefined ? updatedData.rootCauseAnalysis : d.rootCauseAnalysis;
          return { ...d, ...updatedData, rootCauseAnalysis: newRcaArray };
        }
        return d;
      });
      return { ...prev, [line]: { ...ld, disruptions: ud } };
    });
  }, [updateAndSaveProductionData]);
  const removeDisruption = useCallback((line: ProductionLine, id: string, isResolving: boolean = false) => {
    let dta: Disruption | null = null;
    updateAndSaveProductionData(prev => {
      const ld = prev[line];
      if (!ld?.disruptions) return prev;
      dta = ld.disruptions.find(d => d.id === id) || null;
      if (!dta) return prev;
      const ud = ld.disruptions.filter(d => d.id !== id);
      return { ...prev, [line]: { ...ld, disruptions: ud } };
    });
    if (dta) {
      updateAndSaveDisruptionHistory(prevHistory => {
        const now = new Date().toISOString();
        const archivedDisruption = {
          ...dta!,
          resolvedAt: dta!.resolvedAt || now,
          endTime: dta!.endTime || (isResolving ? now : undefined),
          gereedmelddatum: isResolving ? now : dta!.gereedmelddatum,
          rootCauseAnalysis: dta!.rootCauseAnalysis
        };
        if (prevHistory.some(h => h.id === archivedDisruption.id)) {
           console.warn('[removeDisruption] Duplicate history entry prevented:', archivedDisruption.id);
           return prevHistory;
        }
        const newHistory = [archivedDisruption, ...prevHistory];
        return newHistory;
      });
      toast.success("Storing gearchiveerd");
    } else {
      toast.warning("Storing niet gevonden om te archiveren");
    }
  }, [updateAndSaveProductionData, updateAndSaveDisruptionHistory]);
  const addIncident = useCallback((incident: Omit<Incident, 'id' | 'createdAt'>) => { updateAndSaveIncidents(prev => [{ ...incident, id: uuidv4(), createdAt: new Date().toISOString() }, ...prev]); toast.success("Melding toegevoegd"); }, [updateAndSaveIncidents]);
  const updateIncident = useCallback((updatedIncident: Incident) => { updateAndSaveIncidents(prev => prev.map(inc => inc.id === updatedIncident.id ? updatedIncident : inc)); toast.success("Melding bijgewerkt"); }, [updateAndSaveIncidents]);
  const removeIncident = useCallback((id: string, type?: 'safety' | 'quality', addToHistory: boolean = true) => { let ita: Incident | null = null; updateAndSaveIncidents(prev => { const idx = prev.findIndex(i => i.id === id); if (idx === -1) return prev; ita = prev[idx]; return [...prev.slice(0, idx), ...prev.slice(idx + 1)]; }); if (addToHistory && ita) { updateAndSaveVkHistoryData(prev => { const ai = { ...ita!, deletedAt: new Date().toISOString() }; if (prev.some(h => h.id === ai.id)) return prev; return [ai, ...prev].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()); }); toast.success(`Melding gearchiveerd.`); } else if (ita) { toast.warning(`Melding verwijderd.`); } else { toast.error("Kon melding niet vinden."); } }, [updateAndSaveIncidents, updateAndSaveVkHistoryData]);
  const addEquipmentArea = useCallback((line: ProductionLine, areaCode: string, areaLabel: string) => { updateAndSaveEquipmentOptions(prev => addEquipmentAreaToData(prev, line, areaCode, areaLabel)); toast.success(`Gebied '${areaLabel}' toegevoegd.`); }, [updateAndSaveEquipmentOptions]);
  const removeEquipmentArea = useCallback((line: ProductionLine, areaCode: string) => { updateAndSaveEquipmentOptions(prev => removeEquipmentAreaFromData(prev, line, areaCode)); toast.warning(`Gebied '${areaCode}' verwijderd.`); }, [updateAndSaveEquipmentOptions]);
  const editEquipmentArea = useCallback((line: ProductionLine, areaCode: string, updatedAreaCode: string, updatedAreaLabel: string) => { updateAndSaveEquipmentOptions(prev => editEquipmentAreaInData(prev, line, areaCode, updatedAreaCode, updatedAreaLabel)); toast.success(`Gebied '${areaCode}' bijgewerkt.`); }, [updateAndSaveEquipmentOptions]);
  const addEquipmentOption = useCallback((line: ProductionLine, areaCode: string, option: EquipmentEntry) => { updateAndSaveEquipmentOptions(prev => addEquipmentOptionToData(prev, line, areaCode, option)); toast.success(`Apparatuur '${option.label_nl}' toegevoegd.`); }, [updateAndSaveEquipmentOptions]);
  const removeEquipmentOption = useCallback((line: ProductionLine, areaCode: string, optionId: string) => { updateAndSaveEquipmentOptions(prev => removeEquipmentOptionFromData(prev, line, areaCode, optionId)); toast.warning(`Apparatuur verwijderd.`); }, [updateAndSaveEquipmentOptions]);
  const editEquipmentOption = useCallback((line: ProductionLine, areaCode: string, optionId: string, updatedOption: EquipmentEntry) => { updateAndSaveEquipmentOptions(prev => editEquipmentOptionInData(prev, line, areaCode, optionId, updatedOption)); toast.success(`Apparatuur '${updatedOption.label_nl}' bijgewerkt.`); }, [updateAndSaveEquipmentOptions]);
  const archiveAndClearIncidents = useCallback(() => { const ci = incidents; if (ci.length === 0) { toast.info("Geen V&K meldingen te archiveren."); return; } updateAndSaveVkHistoryData(prev => { const ita = ci.map(i => ({ ...i, deletedAt: new Date().toISOString() })); const ch = [...ita, ...prev]; const uh = ch.filter((i, idx, self) => idx === self.findIndex(t => t.id === i.id)); uh.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()); return uh; }); updateAndSaveIncidents(() => []); toast.success(`${ci.length} V&K melding(en) gearchiveerd.`); }, [incidents, updateAndSaveIncidents, updateAndSaveVkHistoryData]);
  const deleteProductionRow = useCallback((line: ProductionLine, date: string) => { updateAndSaveProductionData(prev => { const d = prev[line]; if (!d) return prev; const rows = d.rows.filter(r => r.date !== date); return { ...prev, [line]: { ...d, rows } }; }); toast.error("Productierij permanent verwijderd."); }, [updateAndSaveProductionData]);
  const deleteDisruption = useCallback((line: ProductionLine, id: string) => { updateAndSaveProductionData(prev => { const d = prev[line]; if (!d?.disruptions) return prev; const ud = d.disruptions.filter(dis => dis.id !== id); return { ...prev, [line]: { ...d, disruptions: ud } }; }); toast.error("Lopende storing permanent verwijderd."); }, [updateAndSaveProductionData]);
  const addDowntimeReason = useCallback((line: ProductionLine, reason: DowntimeReason) => { updateAndSaveDowntimeReasons(prev => { const lr = prev[line] || []; if (lr.some(r => r.id === reason.id || r.reason === reason.reason)) { toast.warning("Reden bestaat al."); return prev; } return { ...prev, [line]: [...lr, reason] }; }); toast.success(`Reden '${reason.reason}' toegevoegd.`); }, [updateAndSaveDowntimeReasons]);
  const updateDowntimeReason = useCallback((line: ProductionLine, reasonId: string, updatedReason: DowntimeReason) => { updateAndSaveDowntimeReasons(prev => { const lr = prev[line] || []; const ulr = lr.map(r => r.id === reasonId ? updatedReason : r); return { ...prev, [line]: ulr }; }); toast.success(`Reden '${updatedReason.reason}' bijgewerkt.`); }, [updateAndSaveDowntimeReasons]);
  const removeDowntimeReason = useCallback((line: ProductionLine, reasonId: string) => { updateAndSaveDowntimeReasons(prev => { const lr = prev[line] || []; const ulr = lr.filter(r => r.id !== reasonId); return { ...prev, [line]: ulr }; }); toast.warning(`Reden verwijderd.`); }, [updateAndSaveDowntimeReasons]);
  const updateIndependentDisruption = useCallback((line: ProductionLine, index: number, disruptionData: DisruptionInput | null) => { if (index < 0 || index > 2) { console.error(`Invalid index ${index}`); return; } updateAndSaveIndependentDisruptions(prev => { const ld = [...(prev[line] || [null, null, null])]; ld[index] = disruptionData; return { ...prev, [line]: ld }; }); }, [updateAndSaveIndependentDisruptions]);
  const addLogbookEntry = useCallback((entryData: Omit<LogbookEntry, 'id' | 'timestamp'>) => { updateAndSaveLogbookEntries(prev => { const ne: LogbookEntry = { ...entryData, id: uuidv4(), timestamp: new Date().toISOString() }; return [ne, ...prev]; }); toast.success("Logboek item toegevoegd."); }, [updateAndSaveLogbookEntries]);
  const removeLogbookEntry = useCallback((entryId: string) => { let etm: LogbookEntry | null = null; updateAndSaveLogbookEntries(prev => { const idx = prev.findIndex(e => e.id === entryId); if (idx === -1) { console.warn(`Entry ${entryId} not found`); return prev; } etm = { ...prev[idx] }; return [...prev.slice(0, idx), ...prev.slice(idx + 1)]; }); if (etm) { console.log(`Moving entry ${entryId} to history.`); updateAndSaveLogbookHistory(prev => { if (prev.some(e => e.id === etm!.id)) { console.warn(`Entry ${entryId} already in history.`); return prev; } return [etm!, ...prev]; }); } else { console.error(`Failed to find entry ${entryId}`); toast.error("Fout bij archiveren."); } }, [updateAndSaveLogbookEntries, updateAndSaveLogbookHistory]);
  const updateLogbookEntry = useCallback((updatedEntry: LogbookEntry) => {
    updateAndSaveLogbookEntries(prev => {
      const index = prev.findIndex(entry => entry.id === updatedEntry.id);
      if (index === -1) {
        toast.error("Logboek item niet gevonden om bij te werken.");
        return prev;
      }
      const next = [...prev];
      next[index] = updatedEntry;
      next.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      return next;
    });
    toast.success("Logboek item bijgewerkt.");
  }, [updateAndSaveLogbookEntries]);

  // --- TD Logbook: Add Entry ---
  const addTdLogEntry = useCallback(async (entryInput: TdLogEntryInput) => {
    const timestamp = new Date().toISOString();
    const { shift, teamColor } = getShiftInfo(timestamp);

    if (teamColor === 'Onbekend') {
        toast.error("Kon ploegkleur niet bepalen. Melding niet opgeslagen.");
        console.error("Shift calculation resulted in 'Onbekend'. Aborting addTdLogEntry.");
        return;
    }

    const newEntry: Omit<TdLogEntry, 'id'> = {
      ...entryInput,
      timestamp: timestamp,
      shiftColor: teamColor,
    };

    console.log('[TD Logbook] Attempting to insert:', newEntry);

    const { error } = await supabase
      .from('td_logbook_entries')
      .insert([newEntry]);

    if (error) {
      console.error('[TD Logbook] Error inserting entry:', error);
      toast.error('Fout bij opslaan TD Logboek melding.');
    } else {
      console.log('[TD Logbook] Entry inserted successfully.');
      toast.success('TD Logboek melding opgeslagen.');
    }
  }, []);

  // --- TD Logbook: Update Entry ---
  const updateTdLogEntry = useCallback(async (id: string, updates: Partial<TdLogEntryInput>) => {
    console.log(`[TD Logbook] Attempting to update entry ${id} with:`, updates);
    const { error } = await supabase
      .from('td_logbook_entries')
      .update(updates)
      .eq('id', id);

    if (error) {
      console.error(`[TD Logbook] Error updating entry ${id}:`, error);
      toast.error('Fout bij bijwerken TD Logboek melding.');
    } else {
      console.log(`[TD Logbook] Entry ${id} updated successfully.`);
      toast.success('TD Logboek melding bijgewerkt.');
    }
  }, []);


  // Return the context value
  return {
    productionData, historyData, incidents, equipmentOptions, materialOptions, independentDisruptions, logbookEntries, logbookHistory, tdLogEntries,
    updateLineData, resetData, addMaterialOption, removeMaterialOption, addProductionRow, updateProductionRow, deleteProductionRow, removeProductionRow,
    addDisruption, updateDisruption, deleteDisruption, removeDisruption, addIncident, updateIncident, removeIncident, addToHistory,
    addMaterial: addMaterialOption, deleteMaterial: removeMaterialOption, TARGETS: targets, YIELD_TARGETS: yieldTargets, updateTargets: updateAndSaveTargets,
    updateYieldTargets: updateAndSaveYieldTargets, setProductionData: updateAndSaveProductionData, addEquipmentOption, removeEquipmentOption, editEquipmentOption,
    addEquipmentArea, removeEquipmentArea, editEquipmentArea, clearProductionData, archiveAndClearIncidents, downtimeReasons, addDowntimeReason,
    updateDowntimeReason, removeDowntimeReason, isFlyLocked, setIsFlyLocked, vkHistoryData, disruptionHistory, updateIndependentDisruption,
    addLogbookEntry, removeLogbookEntry, updateLogbookEntry,
    addTdLogEntry, updateTdLogEntry, // Add TD Logbook functions
    setDowntimeReasons: (reasons) => updateAndSaveDowntimeReasons(() => reasons),
    isLoading: false, isSyncing: false, lastSyncTime: null,
  };
};
