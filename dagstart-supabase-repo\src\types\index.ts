export type ProductionLine = 'tl1' | 'tl2' | 'p1' | 'p2' | 'p3' | 'Yard' | 'Koeling' | 'Gebouw' | 'Overige';

// Represents data for a single shift (Ochtend, Middag, Nacht)
export type ProductionShiftData = {
  production: number | string; // Allow string during input
  yield: number | string;      // Allow string during input
  material: string;
  disruptions?: Disruption[]; // <-- Change to Disruption[]
  target?: number; // <-- Add optional target property
  isTransition?: boolean; // Added for material transition switch
};

// Represents a single row in the production table (one day)
export type ProductionRow = {
  id: string; // Add unique ID property
  date: string; // YYYY-MM-DD format
  od: ProductionShiftData; // Ochtenddienst
  md: ProductionShiftData; // Middagdienst
  nd: ProductionShiftData; // Nachtdienst
  // breakdowns?: BreakdownEntry[]; // <-- Removed: Disruptions are now independent
};

// Data specific to one production line
export type LineData = {
  rows: ProductionRow[];
  materials: string[]; // List of available materials for this line
  disruptions: Disruption[]; // <-- Ensure this is Disruption[] (for active disruptions)
  equipmentOptions: Record<string, EquipmentEntry[]>; // Equipment options per area
};

// Overall production data structure
export type ProductionData = Record<ProductionLine, LineData>;

// Represents a breakdown/disruption entry WITHIN a shift/day report
export type BreakdownEntry = {
  id: string;
  equipment: string;
  duration: string; // e.g., "2:15"
  line?: ProductionLine;
  description?: string;
};

// New Type for a single RCA Entry
export interface RcaEntry {
  id: string; // Unique ID for this specific analysis entry
  timestamp: string; // ISO string when this entry was created
  who?: string;
  what?: string;
  where?: string;
  how?: string;
  followUp?: string;
  // solution?: string; // Removed redundant solution field
}

// Type for a single disruption entry (now used everywhere)
export type Disruption = {
  id: string; // Unique ID for the disruption
  line: ProductionLine;
  description: string;
  startTime?: string; // ISO string
  endTime?: string;   // ISO string
  duration?: string; // Calculated duration (e.g., "2h 15m") - likely removed if start/end used
  equipment?: string; // Associated equipment
  pareto?: string;    // Pareto classification <-- Add this field (optional)
  rootCause?: string;
  actions?: string;
  oplossingen?: string; // Main solution field for the disruption
  actionOwner?: string;
  lastUpdate?: string; // ISO string or other date format
  resolveDate?: string; // ISO String - Target date for resolution
  gereedmelddatum?: string; // <-- Add this field
  createdAt?: string; // ISO string - When was it created in the app
  resolvedAt?: string; // ISO string - When was it marked as resolved/removed
  rootCauseAnalysis?: RcaEntry[]; // Array of RCA entries
};

// Input type for creating/updating disruptions (without generated fields)
export type DisruptionInput = Omit<Disruption, 'id' | 'createdAt' | 'resolvedAt' | 'line' | 'rootCauseAnalysis'>; // Exclude RCA array from simple input

// Represents a history entry
export type HistoryEntry = {
  timestamp: string; // ISO string when the entry was created
  line: ProductionLine; // Which line this history entry primarily concerns
  startDate: string; // YYYY-MM-DD
  endDate: string; // YYYY-MM-DD
  data: ProductionData; // Contains rows and disruptions (should be Disruption[]) for the period
};

export interface EquipmentEntry {
  id: string;
  value: string;
  label_nl: string;
  label_en: string;
}

// Voeg definitie toe voor EquipmentOptions
export type EquipmentOptions = Record<ProductionLine, Record<string, EquipmentEntry[]>>;

// Voeg definitie toe voor DowntimeReason
export interface DowntimeReason {
  id: string;
  reason: string;
  // eventueel andere velden zoals 'category', 'code' etc.
}

export interface Incident {
  id: string;
  type: 'safety' | 'quality';
  description: string;
  location: string;
  solution: string;
  reported: 'ja' | 'nee';
  followup: string;
  responsible: string;
  resolveDate: string;
  createdAt: string;
  ltiReported?: boolean;
  accidentReported?: boolean;
  ivOutOfSpec?: boolean;
  korrelOutOfSpec?: boolean;
  machineOutOfSpec?: boolean;
  deletedAt?: string;
}

export interface ProductionContextType {
  productionData: ProductionData;
  historyData: HistoryEntry[];
  incidents: Incident[];
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>;
  materialOptions: Record<ProductionLine, string[]>;
  updateLineData: (line: ProductionLine, data: LineData) => void;
  resetData: () => void;
  addMaterialOption: (line: ProductionLine, material: string) => void;
  removeMaterialOption: (line: ProductionLine, material: string) => void;
  addProductionRow: (line: ProductionLine, date?: string, customRow?: Partial<ProductionRow>) => void;
  updateProductionRow: (line: ProductionLine, date: string, updatedRow: ProductionRow) => void;
  deleteProductionRow: (line: ProductionLine, date: string) => void;
  removeProductionRow: (line: ProductionLine, date: string) => void;
  addDisruption: (line: ProductionLine, disruption: DisruptionInput) => void;
  updateDisruption: (line: ProductionLine, id: string, updates: Partial<Disruption>) => void;
  deleteDisruption: (line: ProductionLine, id: string) => void;
  removeDisruption: (line: ProductionLine, id: string) => void;
  addIncident: (incident: Omit<Incident, 'id'>) => void;
  updateIncident: (id: string, updatedIncident: Incident) => void;
  removeIncident: (id: string) => void;
  addToHistory: (entry: Omit<HistoryEntry, 'timestamp'>) => void;
  addMaterial: (line: ProductionLine, material: string) => void;
  deleteMaterial: (line: ProductionLine, material: string) => void;
  updateEquipmentOptions: (line: ProductionLine, options: Record<string, EquipmentEntry[]>) => void;
  TARGETS: Record<ProductionLine, number>;
  YIELD_TARGETS: Record<ProductionLine, number>;
  updateTargets: (newTargets: Record<ProductionLine, number>) => void;
  updateYieldTargets: (newYieldTargets: Record<ProductionLine, number>) => void;
  setProductionData: React.Dispatch<React.SetStateAction<ProductionData>>;
  addEquipmentOption: (line: ProductionLine, areaCode: string, option: EquipmentEntry) => void;
  removeEquipmentOption: (line: ProductionLine, areaCode: string, optionId: string) => void;
  editEquipmentOption: (line: ProductionLine, areaCode: string, optionId: string, updatedOption: EquipmentEntry) => void;
  addEquipmentArea: (line: ProductionLine, areaCode: string, areaLabel: string) => void;
  removeEquipmentArea: (line: ProductionLine, areaCode: string) => void;
  editEquipmentArea: (line: ProductionLine, areaCode: string, updatedAreaCode: string, updatedAreaLabel: string) => void;
  clearProductionData: (line: ProductionLine) => void;

  // Logbook Types (Placeholder in context type, implementation in hook)
  logbookEntries: LogbookEntry[]; // Now global array
  logbookHistory: LogbookEntry[]; // Now global array
  addLogbookEntry: (entry: Omit<LogbookEntry, 'id' | 'timestamp'>) => void; // No line needed
  removeLogbookEntry: (entryId: string) => void; // No line needed
  // updateLogbookEntry?: (entry: LogbookEntry) => void; // Optional for now
  // removeLogbookEntry?: (line: ProductionLine, entryId: string) => void; // Optional for now
}

// New Type for Logbook Entry
export type LogbookPriority = 'Laag' | 'Middel' | 'Hoog';

export interface LogbookEntry {
  id: string;
  timestamp: string; // ISO string
  text: string;
  priority: LogbookPriority;
  location: string; // Added location field
  // line: ProductionLine; // Line association might be implicit via the Record key
}

// New Type for Shift Color (Based on corrected rotation)
export type ShiftColor = 'Geel' | 'Blauw' | 'Groen' | 'Rood' | 'Wit' | 'Onbekend';

// New Type for TD Logbook Entry
export interface TdLogEntry {
  id: string; // Unique ID (UUID, likely generated by Supabase or frontend)
  timestamp: string; // ISO string, automatically set on creation
  shiftColor: ShiftColor; // Automatically determined based on timestamp
  line: ProductionLine; // Selected production line
  equipment?: string; // Selected machine part (optional?)
  text: string; // The log message from the user
  // isRecurring: boolean; // Removed "Komt terug" flag
  // Add other relevant fields if needed, e.g., user_id
}

// Input type for creating TD Logbook entries (without generated fields)
export type TdLogEntryInput = Omit<TdLogEntry, 'id' | 'timestamp' | 'shiftColor' | 'isRecurring'>; // Removed isRecurring
