import React, { useState, useEffect } from 'react';
import { useProduction } from '@/context/ProductionContext'; // Import context
import { ProductionLine } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Save, AlertTriangle, Check, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav'; // Settings nav
import { Tabs } from '@/components/ui/tabs'; // Tabs wrapper

const Targets: React.FC = () => {
  const {
    TARGETS,
    YIELD_TARGETS,
    updateTargets,
    updateYieldTargets,
    isFlyLocked // Get Fly Lock state
  } = useProduction();

  // --- DEBUGGING --- 
  useEffect(() => {
     console.log("Targets.tsx - isFlyLocked from context:", isFlyLocked);
  }, [isFlyLocked]);
  // --- END DEBUGGING ---

  // Local state to manage form inputs before saving to context
  const [localTargets, setLocalTargets] = useState<Record<ProductionLine, number>>({ ...TARGETS });
  const [localYieldTargets, setLocalYieldTargets] = useState<Record<ProductionLine, number>>({ ...YIELD_TARGETS });
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Sync local state if context changes externally (e.g., initial load)
  useEffect(() => {
    setLocalTargets({ ...TARGETS });
    setLocalYieldTargets({ ...YIELD_TARGETS });
    setHasChanges(false); // Reset changes on external update
  }, [TARGETS, YIELD_TARGETS]);

  const handleTargetChange = (line: ProductionLine, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 0) {
      setLocalTargets(prev => ({ ...prev, [line]: numValue }));
      setHasChanges(true);
      setSaveStatus('idle');
    }
  };

  const handleYieldTargetChange = (line: ProductionLine, value: string) => {
    const numValue = parseInt(value, 10);
     // Allow values between 0 and 100 for yield percentage
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 100) { 
      setLocalYieldTargets(prev => ({ ...prev, [line]: numValue }));
      setHasChanges(true);
      setSaveStatus('idle');
    }
  };

  const handleSave = async () => {
    if (!hasChanges) return;
    setIsSaving(true);
    setSaveStatus('idle');
    try {
      // Call context functions to update the actual state
      await updateTargets(localTargets);
      await updateYieldTargets(localYieldTargets);
      setHasChanges(false);
      setSaveStatus('success');
      toast.success("Targets opgeslagen.");
      setTimeout(() => setSaveStatus('idle'), 2000); // Reset status indicator
    } catch (error) {
      console.error("Error saving targets:", error);
      toast.error("Fout bij opslaan targets.");
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };
  
  const productionLines: ProductionLine[] = ['tl1', 'tl2', 'p1', 'p2', 'p3'];

  return (
    <div className="p-6 space-y-6">
      <Tabs value="targets"> 
        <SettingsSubNav />
      </Tabs>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-faerch-blue">Target & Yield Beheer</h1>
        <Button 
           onClick={handleSave} 
           disabled={!hasChanges || isSaving || isFlyLocked} // Disable save on lock
        >
          {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 
           saveStatus === 'success' ? <Check className="mr-2 h-4 w-4" /> : 
           saveStatus === 'error' ? <AlertTriangle className="mr-2 h-4 w-4 text-red-500" /> : 
           <Save className="mr-2 h-4 w-4" />}
          {isSaving ? 'Opslaan...' : saveStatus === 'success' ? 'Opgeslagen' : 'Wijzigingen Opslaan'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Production Targets Card */}
        <div className="dashboard-card">
           <div className="p-4 border-b">
             <h2 className="text-xl font-semibold">Productie Targets (per shift)</h2>
          </div>
          <div className="p-4 space-y-4">
             {productionLines.map(line => (
                 <div key={line + '-target'} className="flex items-center justify-between space-x-4">
                    <Label htmlFor={`${line}-target`} className="font-medium w-1/4">{line.toUpperCase()}</Label>
                    <Input 
                       id={`${line}-target`}
                       type="number"
                       min="0"
                       value={localTargets[line] || ''} 
                       onChange={(e) => handleTargetChange(line, e.target.value)}
                       className="flex-grow"
                       disabled={isFlyLocked} // Disable input on lock
                    />
                 </div>
             ))}
          </div>
        </div>

        {/* Yield Targets Card */}
        <div className="dashboard-card">
           <div className="p-4 border-b">
             <h2 className="text-xl font-semibold">Yield Targets (%)</h2>
          </div>
          <div className="p-4 space-y-4">
             {productionLines.map(line => (
                 <div key={line + '-yield'} className="flex items-center justify-between space-x-4">
                    <Label htmlFor={`${line}-yield`} className="font-medium w-1/4">{line.toUpperCase()}</Label>
                    <Input 
                       id={`${line}-yield`}
                       type="number"
                       min="0"
                       max="100"
                       value={localYieldTargets[line] || ''} 
                       onChange={(e) => handleYieldTargetChange(line, e.target.value)}
                       className="flex-grow"
                       disabled={isFlyLocked} // Disable input on lock
                    />
                 </div>
             ))}
          </div>
        </div>
      </div>
    </div>
  );
}; 