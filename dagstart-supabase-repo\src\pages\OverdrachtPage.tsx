import React, { useState, useEffect, useMemo, useCallback, useRef, FC } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'; // Import Dialog components
import { BookText } from 'lucide-react'; // Import an icon for the button
import { useProduction } from '@/context/ProductionContext';
import { ProductionLine, ProductionRow, Disruption, LineData, ProductionData } from '@/types';
import LineSelector from '@/components/common/LineSelector';
import Logbook from '@/components/dashboard/Logbook';
import IndependentDisruptionInput from '@/components/report/IndependentDisruptionInput';
import ShiftForm from '@/components/report/ShiftForm';
import DaySelector from '@/components/report/DaySelector';
// import DaySummary from '@/components/report/DaySummary'; // Removed import
import DeleteDayDialog from '@/components/report/DeleteDayDialog';
import ShiftInputDialog from '@/components/report/ShiftInputDialog';
import DateSelector from '@/components/report/DateSelector';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format, parseISO, differenceInMinutes, isValid } from 'date-fns';
import { nl } from 'date-fns/locale';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { Plus, Save, Check, AlertTriangle, Loader2, Trash2, Edit, Printer, AlertCircle } from 'lucide-react';
import { useReportForm } from '@/hooks/report/useReportForm';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useReactToPrint } from 'react-to-print';
import { useTransaction } from '@/context/TransactionContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import DaySummary from '@/components/report/DaySummary'; // Re-added import
import { loadItem } from '@/lib/local-storage'; // Import loadItem

// Helper function to get line color classes
const getLineColorClass = (line: ProductionLine, type: 'bg' | 'text' | 'border'): string => {
  const colors = {
    tl1: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
    tl2: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
    p1: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
    p2: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200' },
    p3: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
  };
  return colors[line]?.[type] || 'bg-gray-100 text-gray-800 border-gray-200'; // Default fallback
};

// Helper function to format duration in minutes to HH:MM
const formatDuration = (totalMinutes: number): string => {
  if (isNaN(totalMinutes) || totalMinutes < 0) return "0:00";
  const hours = Math.floor(totalMinutes / 60);
  const minutes = Math.round(totalMinutes % 60);
  return `${hours}:${minutes.toString().padStart(2, '0')}`;
};

// Helper function to calculate duration for active disruptions
const calculateActiveDuration = (disruption: Disruption): number => {
  const now = new Date();
  const startTimeStr = disruption.startTime || disruption.createdAt;
  if (!startTimeStr) return 0;
  try {
    const startTime = parseISO(startTimeStr);
    if (!isValid(startTime)) return 0;
    return differenceInMinutes(now, startTime);
  } catch (e) {
    return 0;
  }
};

// Corrected component name
const OverdrachtPage: FC = () => {
  const {
    productionData,
    updateProductionRow,
    addProductionRow,
    materialOptions,
    removeProductionRow,
    addToHistory,
    TARGETS,
    YIELD_TARGETS,
    independentDisruptions,
    updateIndependentDisruption,
    archiveAndClearIncidents, // Needed for save function if VK clearing is desired
    isFlyLocked,
    removeDisruption, // Need this for archiving disruptions
    incidents, // Need this to check if V&K should be archived
    logbookEntries, // Get logbook entries
  } = useProduction();

  // Use the refactored hook
  const {
    selectedLine,
    activeRow,
    activeRowIndex,
    lineData,
    handleLineChange,
    handleRowChange,
    handleShiftChange,
    handleAddDay,
    handleDateSelect,
    handleDeleteDay,
    handleConfirmDeleteDay,
    calculateTotalProduction,
    dateDialogOpen,
    setDateDialogOpen,
    selectedDate,
    setSelectedDate,
    showDeleteDayDialog,
    setShowDeleteDayDialog,
    dayToDelete,
    isShiftInputOpen,
    setIsShiftInputOpen,
    pendingRowDate,
    handleShiftInputSubmit,
    handleEditDay,
    editingRowData,
    lastFieldChangeTime,
  } = useReportForm();

  // Local state for saving status specific to this page
  const [isSaving, setIsSaving] = useState(false);
  const [saveVariant, setSaveVariant] = useState<'default' | 'success' | 'error'>('default');
  const [saveText, setSaveText] = useState('Archiveren'); // Renamed button text
  const [isModified, setIsModified] = useState(false); // Track modifications

  const componentRef = useRef<HTMLDivElement>(null);

  // Print functionaliteit
  const handlePrint = useReactToPrint({
    documentTitle: `Overdracht ${selectedLine}`,
  });

  // Autosave effect
   useEffect(() => {
     if (!lastFieldChangeTime || !activeRow) return;
     setIsModified(true);
     console.log('Change detected, marked as modified.');
   }, [lastFieldChangeTime, activeRow]);

   // Warning on page leave
   useEffect(() => {
     const handleBeforeUnload = (e: BeforeUnloadEvent) => {
       const hasUnsavedDisruptions = independentDisruptions[selectedLine]?.some(d => d !== null);
       if (isModified || hasUnsavedDisruptions) {
         const message = "Er zijn mogelijk onopgeslagen wijzigingen. Weet u zeker dat u deze pagina wilt verlaten?";
         e.returnValue = message; return message;
       }
     };
     window.addEventListener('beforeunload', handleBeforeUnload);
     return () => window.removeEventListener('beforeunload', handleBeforeUnload);
   }, [isModified, independentDisruptions, selectedLine]);


  // --- Updated Archive Logic ---
  const handleArchiveCurrentLine = async () => {
    const line = selectedLine;
    const lineDataToProcess = productionData[line];
    const rowsToProcess = lineDataToProcess?.rows || [];
    // Get INDEPENDENT disruptions for the current line that are not null
    const independentDisruptionsToArchive = independentDisruptions[line]?.filter(d => d !== null) || [];

    // Check if there are rows OR independent disruptions OR V&K incidents to archive
    if (rowsToProcess.length === 0 && independentDisruptionsToArchive.length === 0 && incidents.length === 0) {
      toast.info(`Geen productie dagen, storingen of V&K meldingen om te archiveren voor ${line.toUpperCase()}.`);
      return; // Exit if nothing to archive
    }

    setIsSaving(true); setSaveText(`Archiveren ${line.toUpperCase()}...`); setSaveVariant('default');
    let errorCount = 0; const errors: string[] = [];
    let rowsArchived = 0;
    let independentDisruptionsArchived = 0; // Renamed variable
    let vkArchived = 0;

    // 1. Archive Production Rows
    if (rowsToProcess.length > 0) {
        const startDate = rowsToProcess.reduce((min, row) => new Date(row.date) < new Date(min) ? row.date : min, rowsToProcess[0].date);
        const endDate = rowsToProcess.reduce((max, row) => new Date(row.date) > new Date(max) ? row.date : max, rowsToProcess[0].date);
        const lineDataForHistory: LineData = {
            rows: [...rowsToProcess], // Create a copy
            // Only include disruptions already associated with the line data being archived
            disruptions: lineDataToProcess?.disruptions || [],
            materials: lineDataToProcess.materials || [],
            equipmentOptions: lineDataToProcess.equipmentOptions || {}
        };
        const dataForHistory: Partial<ProductionData> = { [line]: lineDataForHistory };

        console.log(`Archiveren productierijen voor lijn ${line}: ${rowsToProcess.length} rijen...`);
        let historyAddedSuccessfully = false; // Flag to track success
        try {
          console.log('[Archive Debug] Calling addToHistory with data:', { startDate, endDate, data: dataForHistory, line });
          addToHistory({ startDate, endDate, data: dataForHistory as ProductionData, line });
          rowsArchived = rowsToProcess.length;
          independentDisruptionsArchived = independentDisruptionsToArchive.length;
          historyAddedSuccessfully = true; // Mark success
          console.log('[Archive Debug] addToHistory succeeded.');
        } catch (error) {
          console.error('[Archive Debug] addToHistory FAILED:', error); // Log specific error
          console.error(`Fout bij archiveren productie/storingen voor lijn ${line}:`, error);
          errors.push(`Fout bij archiveren productie/storingen: ${error}`);
          errorCount++;
        }

        // Clear UI elements ONLY if history was added successfully
        if (historyAddedSuccessfully) {
            console.log(`Wissen UI elementen voor lijn ${line}...`);
            try {
                // 1. Clear independent disruption slots
                console.log('Wissen onafhankelijke storingen...');
                [0, 1, 2].forEach(index => updateIndependentDisruption(line, index, null));

                // 2. Remove the archived production rows from the UI
                if (rowsArchived > 0) {
                    console.log(`[Archive Debug] Attempting to remove ${rowsArchived} rows using DATE.`);
                    const archivedRowDates = rowsToProcess.map(r => r.date); // Use date instead of id
                    console.log('[Archive Debug] Archived Row Dates:', archivedRowDates);
                    const currentRows = productionData[line]?.rows || [];
                    console.log('[Archive Debug] Current Rows in Context (Dates):', JSON.stringify(currentRows.map(r => r.date))); // Log current dates
                    const indicesToDelete = currentRows
                        .map((row, index) => {
                            const shouldDelete = archivedRowDates.includes(row.date); // Compare dates
                            console.log(`[Archive Debug] Checking row DATE ${row.date} at index ${index}. Should delete: ${shouldDelete}`);
                            return shouldDelete ? index : -1;
                        })
                        .filter(index => index !== -1);

                    console.log('[Archive Debug] Indices to delete (before sort):', indicesToDelete);
                    indicesToDelete.sort((a, b) => b - a); // Sort descending
                    console.log('[Archive Debug] Indices to delete (after sort):', indicesToDelete);


                    indicesToDelete.forEach(index => {
                        // Get the date of the row at the index we are about to delete
                        const dateToDelete = currentRows[index]?.date;
                        if (!dateToDelete) {
                            console.error(`[Archive Debug] Could not find date for row at index ${index}. Skipping removal.`);
                            errors.push(`Kon datum niet vinden voor rij op index ${index}.`);
                            errorCount++;
                            return; // Skip if date is somehow missing
                        }
                        console.log(`[Archive Debug] Calling removeProductionRow for line ${line}, DATE ${dateToDelete}`);
                        try {
                            // Assuming removeProductionRow can handle removal by date OR index.
                            // Let's use DATE for robustness, as index might shift if context updates mid-loop.
                            // Need to check removeProductionRow definition. Assuming it takes date.
                            removeProductionRow(line, dateToDelete); // Pass DATE instead of index
                        } catch (removeError) {
                            console.error(`Fout bij verwijderen gearchiveerde rij met datum ${dateToDelete}:`, removeError);
                            errors.push(`Fout bij verwijderen gearchiveerde rij ${dateToDelete}: ${removeError}`);
                            errorCount++;
                        }
                    });
                }

                console.log(`UI elementen gewist/gereset voor lijn ${line}.`);
            } catch (clearError) {
                console.error(`Fout bij wissen/resetten UI elementen na archiveren voor lijn ${line}:`, clearError);
                errors.push(`Fout bij wissen/resetten UI na archiveren: ${clearError}`);
                errorCount++; // Increment error count if clearing fails
            }
        }
    }

    // Removed the old logic for archiving active row-specific disruptions (lines 188-202)
    // Independent disruptions are now handled within the main try block (step 1)
    // 3. Conditionally Archive V&K Incidents
    const shouldSaveVK = loadItem<boolean>('saveVK', false); // Check setting from local storage
    if (shouldSaveVK && incidents.length > 0) {
        console.log('Archiveren en wissen van Veiligheid & Kwaliteit meldingen...');
        try {
            archiveAndClearIncidents();
            vkArchived = incidents.length; // Assume all were archived if no error
        }
        catch (error) {
            console.error('Fout bij archiveren van V&K meldingen:', error);
            errors.push(`Fout bij archiveren V&K meldingen: ${error}`);
            errorCount++;
        }
    }

    // 4. Finalize
    setIsSaving(false);
    setIsModified(false); // Reset modified state
    if (errorCount === 0) {
      setSaveText('Gearchiveerd'); setSaveVariant('success');
      let successMsg = `Archiveren succesvol voor ${line.toUpperCase()}.`;
      if (rowsArchived > 0) successMsg += ` ${rowsArchived} dag(en) verplaatst.`;
      if (independentDisruptionsArchived > 0) successMsg += ` ${independentDisruptionsArchived} onafhankelijke storing(en) verplaatst.`;
      if (vkArchived > 0) successMsg += ` ${vkArchived} V&K melding(en) verplaatst.`;
      toast.success(successMsg);
    } else {
      setSaveText('Archiveren Mislukt'); setSaveVariant('error');
      toast.error(`Archiveren mislukt met ${errorCount} fout(en).`, { description: errors.join('; '), duration: 10000 });
    }
    setTimeout(() => { setSaveVariant('default'); setSaveText('Archiveren'); }, 3000); // Reset button text
  };
  // --- END Archive Logic ---

  // Format function for display
  const formatForDisplayLocal = (value: string | number | undefined): string => {
      if (value === undefined || value === null) return '';
      return String(value).replace('.', ',');
  };

  // Calculate summary for the *currently selected* activeRow
  const currentDaySummary = useMemo(() => {
    if (!activeRow) return { totalProduction: 0, averageYield: 0 };
    const prod = (Number(activeRow.od.production) || 0) + (Number(activeRow.md.production) || 0) + (Number(activeRow.nd.production) || 0);
    const yields = [Number(activeRow.od.yield) || 0, Number(activeRow.md.yield) || 0, Number(activeRow.nd.yield) || 0];
    const validYields = yields.filter(y => y > 0);
    const avgYield = validYields.length > 0 ? validYields.reduce((a, b) => a + b, 0) / validYields.length : 0;
    return { totalProduction: prod, averageYield: avgYield };
  }, [activeRow]);

  const currentDayTarget = useMemo(() => (TARGETS[selectedLine] || 0) * 3, [selectedLine, TARGETS]);
  const currentYieldTarget = useMemo(() => YIELD_TARGETS?.[selectedLine] || 0, [selectedLine, YIELD_TARGETS]);

  // Calculate Top 3 Disruptions (kept for potential future use)
  const topDisruptions = useMemo(() => {
    const activeDisruptions = productionData[selectedLine]?.disruptions?.filter(d => !d.endTime) || [];
    return activeDisruptions
      .map(d => ({
        ...d,
        activeDuration: calculateActiveDuration(d)
      }))
      .sort((a, b) => b.activeDuration - a.activeDuration)
      .slice(0, 3);
  }, [productionData, selectedLine]);


  // Calculate logbook priority counts
  const logbookPriorityCounts = useMemo(() => {
    const counts = { Hoog: 0, Middel: 0, Laag: 0 };
    logbookEntries.forEach(entry => {
      if (entry.priority === 'Hoog') counts.Hoog++;
      else if (entry.priority === 'Middel') counts.Middel++;
      else if (entry.priority === 'Laag') counts.Laag++;
    });
    return counts;
  }, [logbookEntries]);

  return (
    <div className="animate-fade-in space-y-6 pb-8">
      {/* Removed Title */}

      {/* Removed Logbook Section */}

      {/* Logbook Priority Counts Display */}
      <div className="mb-2 flex justify-start items-center gap-4 text-xs text-gray-600 px-2">
        <span className="font-medium">Logboek Prio:</span>
        <span className={logbookPriorityCounts.Hoog > 0 ? 'text-red-600 font-semibold' : ''}>Hoog: {logbookPriorityCounts.Hoog}</span>
        <span className={logbookPriorityCounts.Middel > 0 ? 'text-yellow-600 font-semibold' : ''}>Middel: {logbookPriorityCounts.Middel}</span>
        <span className={logbookPriorityCounts.Laag > 0 ? 'text-gray-500 font-semibold' : ''}>Laag: {logbookPriorityCounts.Laag}</span>
      </div>

      {/* Line Selector and Save Button Bar */}
      <div className="bg-gray-100 p-2 rounded-md shadow-sm mb-6 flex justify-between items-center">
        <div className="flex flex-wrap gap-2 items-center"> {/* Wrapper for LineSelector and Logbook button */}
          <LineSelector selectedLine={selectedLine} onChange={handleLineChange} />
          {/* Logbook Dialog Trigger Button */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="ml-2">
                <BookText className="h-4 w-4 mr-1" /> Logboek
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl h-[80vh] flex flex-col"> {/* Adjust size as needed */}
              <DialogHeader>
                <DialogTitle>Logboek</DialogTitle>
              </DialogHeader>
              <div className="flex-grow overflow-y-auto pr-2"> {/* Make content scrollable */}
                <Logbook />
              </div>
            </DialogContent>
          </Dialog>
        </div>
        <div className="flex items-center gap-2"> {/* Existing div for Archive button */}
           {/* Updated Archive Button */}
           <Button onClick={handleArchiveCurrentLine} size="sm" disabled={isSaving || isFlyLocked}>
             {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : saveVariant === 'success' ? <Check className="h-4 w-4 mr-2" /> : saveVariant === 'error' ? <AlertTriangle className="h-4 w-4 mr-2 text-red-500" /> : <Save className="h-4 w-4 mr-2" />}
             {saveText} {/* Text is now "Archiveren" */}
           </Button>
        </div>
      </div>

      {/* Main Content Area: Day Selector, Shift Details, and Day Summary */}
      <div className="flex flex-col lg:flex-row gap-6 mb-6">
        {/* Day Selector */}
        <div className="lg:basis-1/4 border rounded-md p-4 bg-white shadow-sm flex flex-col"> {/* Made narrower */}
          <div className="flex justify-between items-center mb-4 flex-shrink-0">
            <h3 className="text-lg font-semibold">Dagen</h3>
            <Button onClick={handleAddDay} size="sm" variant="outline">
              <Plus className="h-4 w-4 mr-1" /> Nieuwe Dag
            </Button>
          </div>
           <div className="flex-grow overflow-y-auto pr-2">
             <DaySelector
               lineData={lineData || { rows: [], materials: [], disruptions: [], equipmentOptions: {} }}
               activeRowIndex={activeRowIndex}
               onRowChange={handleRowChange}
               onDeleteDay={handleDeleteDay}
               onEditDay={handleEditDay}
               selectedLine={selectedLine}
             />
           </div>
        </div>

        {/* Shift Details & Day Summary Container */}
        <div className="lg:basis-2/3 flex flex-col md:flex-row gap-6">
            {/* Shift Details */}
            <div className="flex-grow border rounded-md p-4 bg-white shadow-sm">
              {activeRow ? (
                <>
                  <h3 className="text-lg font-semibold mb-4">
                    Details voor {format(new Date(activeRow.date + 'T00:00:00'), 'EEEE d MMMM yyyy', { locale: nl })}
                  </h3>
                  <Tabs defaultValue="od" className="w-full">
                    <TabsList className="grid w-full grid-cols-3 mb-4">
                      <TabsTrigger value="od">Ochtend</TabsTrigger>
                      <TabsTrigger value="md">Middag</TabsTrigger>
                      <TabsTrigger value="nd">Nacht</TabsTrigger>
                    </TabsList>
                    <TabsContent value="od">
                      <ShiftForm
                        shift="od"
                        shiftLabel="Ochtenddienst"
                        shiftData={activeRow.od}
                        onShiftChange={handleShiftChange}
                        materialOptions={materialOptions[selectedLine] || []}
                        isDisabled={false}
                        formatForDisplay={formatForDisplayLocal}
                        shiftTarget={TARGETS[selectedLine]}
                      />
                    </TabsContent>
                    <TabsContent value="md">
                      <ShiftForm
                        shift="md"
                        shiftLabel="Middagdienst"
                        shiftData={activeRow.md}
                        onShiftChange={handleShiftChange}
                        materialOptions={materialOptions[selectedLine] || []}
                        isDisabled={false}
                        formatForDisplay={formatForDisplayLocal}
                        shiftTarget={TARGETS[selectedLine]}
                      />
                    </TabsContent>
                    <TabsContent value="nd">
                      <ShiftForm
                        shift="nd"
                        shiftLabel="Nachtdienst"
                        shiftData={activeRow.nd}
                        onShiftChange={handleShiftChange}
                        materialOptions={materialOptions[selectedLine] || []}
                        isDisabled={false}
                        formatForDisplay={formatForDisplayLocal}
                        shiftTarget={TARGETS[selectedLine]}
                      />
                    </TabsContent>
                  </Tabs>
                </>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Selecteer een dag of voeg een nieuwe dag toe.
                </div>
              )}
            </div>

             {/* Day Summary (Only if activeRow exists) - Stacked Vertically */}
             {activeRow && (
                <div className="md:w-1/4 flex-shrink-0 space-y-2">
                   <DaySummary
                     totalProduction={currentDaySummary.totalProduction}
                     target={currentDayTarget}
                     averageYield={currentDaySummary.averageYield}
                     yieldTarget={currentYieldTarget}
                   />
                 </div>
             )}
        </div>
      </div>

      {/* Independent Disruptions Input Section */}
      <IndependentDisruptionInput
        line={selectedLine}
        disruptions={independentDisruptions[selectedLine] || [null, null, null]}
        onUpdate={updateIndependentDisruption}
      />

      {/* Dialogs */}
      <ShiftInputDialog
        open={isShiftInputOpen}
        onOpenChange={setIsShiftInputOpen}
        onSubmit={handleShiftInputSubmit}
        selectedDate={pendingRowDate}
        selectedLine={selectedLine}
        materialOptions={materialOptions[selectedLine] || []}
        target={TARGETS[selectedLine] ? TARGETS[selectedLine] * 3 : undefined}
        initialData={editingRowData}
      />

      <AlertDialog open={showDeleteDayDialog} onOpenChange={setShowDeleteDayDialog}>
          <AlertDialogContent>
              <AlertDialogHeader>
                  <AlertDialogTitle>Weet u het zeker?</AlertDialogTitle>
                  <AlertDialogDescription>
                      Deze actie kan niet ongedaan worden gemaakt. Dit verwijdert de productiedata voor {dayToDelete?.date ? format(new Date(dayToDelete.date + 'T00:00:00'), 'dd-MM-yyyy', { locale: nl }) : 'de geselecteerde dag'}.
                  </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                  <AlertDialogCancel>Annuleren</AlertDialogCancel>
                  <AlertDialogAction onClick={handleConfirmDeleteDay} className="bg-red-600 hover:bg-red-700">Verwijderen</AlertDialogAction>
              </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>

      <DateSelector
        open={dateDialogOpen}
        onOpenChange={setDateDialogOpen}
        onSelect={handleDateSelect}
        selectedDate={selectedDate}
      />

      {/* Hidden printable component */}
      <div style={{ display: 'none' }}>
        <div ref={componentRef}>
          <h1>Print Overdracht voor {selectedLine}</h1>
          {/* TODO: Add relevant content for printing */}
        </div>
      </div>

    </div>
  );
};

export default OverdrachtPage;