
import React from 'react';
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DayPicker } from 'react-day-picker';
import { nl } from 'date-fns/locale';

interface DateSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDate: Date | undefined;
  onSelect: (date: Date | undefined) => void;
}

const DateSelector: React.FC<DateSelectorProps> = ({
  open,
  onOpenChange,
  selectedDate,
  onSelect
}) => {
  // Disable future dates
  const disabledDays = { after: new Date() };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Selecteer een datum</DialogTitle>
          <DialogDescription>
            Kies een datum voor de productiedag.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <DayPicker
            mode="single"
            selected={selectedDate}
            onSelect={onSelect}
            locale={nl}
            disabled={disabledDays}
            className="pointer-events-auto"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Annuleren</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DateSelector;
