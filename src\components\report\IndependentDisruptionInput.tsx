import React, { useState, useEffect, useCallback } from 'react';
import { ProductionLine, DisruptionInput, EquipmentEntry } from '@/types';
import { Input } from '@/components/ui/input';
import { Textarea as ShadcnTextarea } from '@/components/ui/textarea'; // Renamed to avoid conflict
import TextareaAutosize from 'react-textarea-autosize';
import { Label } from '@/components/ui/label';
import EquipmentSelector from '../common/EquipmentSelector'; // Changed to relative path
import { useProduction } from '@/context/ProductionContext'; // To get equipment options
import { Button } from '@/components/ui/button'; // Import Button
import { PlusCircle } from 'lucide-react'; // Import Icon
import { toast } from 'sonner'; // Import toast
interface IndependentDisruptionInputProps {
  line: ProductionLine;
  disruptions: (DisruptionInput | null)[]; // Array of exactly 3 items
  onUpdate: (line: ProductionLine, index: number, data: DisruptionInput | null) => void;
}

const IndependentDisruptionInput: React.FC<IndependentDisruptionInputProps> = ({
  line,
  disruptions,
  onUpdate,
}) => {
  const { equipmentOptions } = useProduction();
  // Get the equipment options for the specific line (grouped by area)
  const groupedLineEquipmentOptions = equipmentOptions[line] || {};
  // Flatten the options from all areas into a single array for the selector
  const flatLineEquipmentOptions: EquipmentEntry[] = Object.values(groupedLineEquipmentOptions).flat();

  // Local state to manage input values independently
  const [localDisruptions, setLocalDisruptions] = useState<(DisruptionInput | null)[]>([]);

  // Effect to initialize and sync local state with prop changes
  useEffect(() => {
    // Ensure the local state always has 3 elements, matching the prop structure
    const initialLocalState = [0, 1, 2].map(i => disruptions[i] || null);
    setLocalDisruptions(initialLocalState);
  }, [disruptions]); // Re-sync if the external disruptions prop changes


  // Update LOCAL state on input change
  const handleLocalInputChange = (
    index: number,
    field: keyof DisruptionInput,
    value: string | undefined | null
  ) => {
    setLocalDisruptions(prev => {
      const newState = [...prev];
      const currentData = newState[index] || { description: '' }; // Ensure description exists
      newState[index] = {
        ...currentData,
        [field]: value,
      };
      return newState;
    });
  };

  // Debounce the update to the global state
  useEffect(() => {
    const handler = setTimeout(() => {
      // Compare local state with the prop state and update if different
      localDisruptions.forEach((localData, index) => {
        const propData = disruptions[index];
        if (JSON.stringify(localData) !== JSON.stringify(propData)) {
          const isEmpty = !localData || (!localData.duration && !localData.equipment && !localData.description);
          console.log(`Debounced update for index ${index}:`, isEmpty ? null : localData);
          onUpdate(line, index, isEmpty ? null : localData);
        }
      });
    }, 500); // 500ms debounce delay

    // Cleanup function to clear the timeout if the effect re-runs
    return () => {
      clearTimeout(handler);
    };
    // Dependencies: localDisruptions, line, onUpdate, disruptions (for comparison)
  }, [localDisruptions, line, onUpdate, disruptions]);

  return (
    <div className="dashboard-card p-4 mt-6 border bg-white rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-4 border-b pb-2">
        <h2 className="text-lg font-semibold">Storingen / Downtime</h2>
        {/* "Extra Melding" Button Removed */}
      </div>
      <div className="space-y-4">
        {/* Map over the local state */}
        {localDisruptions.map((disruption, index) => {
          const uniqueId = `${line}-disruption-${index}`; // Base for unique IDs

          return (
            <div key={index} className="border rounded p-3 bg-gray-50/50 relative">
              {/* Optional: Add delete button if a slot should be clearable */}
              {/* <button onClick={() => onUpdate(line, index, null)}>Clear</button> */}
              <div className="flex flex-row items-start gap-4 md:gap-6">
                {/* Downtime Input */}
                {/* Downtime Input - Fixed width */}
                <div className="w-[100px] flex-shrink-0">
                  <Label htmlFor={`${uniqueId}-duration`} className="text-sm font-medium">Downtime</Label>
                  <Input
                    id={`${uniqueId}-duration`}
                    type="text" // Consider time input or validation
                    placeholder="00:00"
                    value={disruption?.duration || ''}
                    onChange={(e) => handleLocalInputChange(index, 'duration', e.target.value)}
                    // Removed onBlur
                    className="mt-1 text-base md:text-sm"
                  />
                </div>

                {/* Equipment Selector */}
                {/* Equipment Selector - Fixed width */}
                <div className="w-[250px] flex-shrink-0">
                  <Label htmlFor={`${uniqueId}-equipment`} className="text-sm font-medium">Onderdeel</Label>
                  <EquipmentSelector
                     id={`${uniqueId}-equipment`}
                     equipmentOptions={flatLineEquipmentOptions}
                     value={disruption?.equipment || ''}
                     onChange={(value) => handleLocalInputChange(index, 'equipment', value)}
                     // Removed onBlur logic
                     className="mt-1 w-full"
                     placeholder="Selecteer of typ onderdeel..."
                  />
                </div>

                {/* Description/Pareto Input */}
                {/* Description/Pareto Input - Fills remaining space */}
                <div className="flex-grow">
                  <Label htmlFor={`${uniqueId}-description`} className="text-sm font-medium">Pareto / Omschrijving</Label>
                  <TextareaAutosize
                    id={`${uniqueId}-description`}
                    minRows={1} // Start with 1 row, grows automatically
                    value={disruption?.description || ''}
                    onChange={(e) => handleLocalInputChange(index, 'description', e.target.value)}
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1" // Applied Shadcn styles, removed fixed height
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Wrap component in React.memo to prevent unnecessary re-renders
const MemoizedIndependentDisruptionInput = React.memo(IndependentDisruptionInput);
export default MemoizedIndependentDisruptionInput;