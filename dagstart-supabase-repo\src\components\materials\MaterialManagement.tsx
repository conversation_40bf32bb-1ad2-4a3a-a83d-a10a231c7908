import React, { useState, useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Trash2, Search } from 'lucide-react';
import { useProduction } from '@/context/ProductionContext';
import { ProductionLine } from '@/types';
import toast from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import LineSelector from '@/components/common/LineSelector';

// Definieer MaterialState lokaal als het niet globaal beschikbaar is
interface MaterialManagementState {
  isAddMaterialDialogOpen: boolean;
}

export const MaterialManagement: React.FC = () => {
  const {
    materialOptions,
    addMaterialOption,
    removeMaterialOption,
    isFlyLocked,
    productionData
  } = useProduction();
  
  // State specifiek voor deze component
  const [state, setState] = useState<MaterialManagementState>({
    isAddMaterialDialogOpen: false
  });
  const [dialogSelectedLines, setDialogSelectedLines] = useState<ProductionLine[]>([]);
  const [dialogMaterialTypes, setDialogMaterialTypes] = useState<string[]>([]);
  const [dialogNewMaterial, setDialogNewMaterial] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [newMaterial, setNewMaterial] = useState('');

  const currentMaterials = useMemo(() => {
    return productionData[selectedLine]?.materials || [];
  }, [productionData, selectedLine]);

  const handleAddMaterial = () => {
    if (newMaterial.trim() === '') {
      toast.error("Materiaalnaam mag niet leeg zijn.");
      return;
    }
    if (currentMaterials.includes(newMaterial.trim())) {
      toast.warning("Dit materiaal bestaat al voor deze lijn.");
      return;
    }
    addMaterialOption(selectedLine, newMaterial.trim());
    toast.success(`Materiaal "${newMaterial.trim()}" toegevoegd aan lijn ${selectedLine.toUpperCase()}.`);
    setNewMaterial('');
  };

  const handleRemoveMaterial = (materialToRemove: string) => {
    removeMaterialOption(selectedLine, materialToRemove);
    toast.warning(`Materiaal "${materialToRemove}" verwijderd van lijn ${selectedLine.toUpperCase()}.`);
  };

  const handleDialogAddMaterial = () => {
    if (!dialogNewMaterial.trim()) {
      toast.error("Materiaal naam kan niet leeg zijn");
      return;
    }
    if (dialogSelectedLines.length === 0) {
      toast.error("Selecteer ten minste één productielijn");
      return;
    }

    const formattedMaterial = dialogNewMaterial.charAt(0).toUpperCase() + dialogNewMaterial.slice(1).toLowerCase();
    const hasTL1 = dialogSelectedLines.includes('tl1');
    const hasTL2 = dialogSelectedLines.includes('tl2');
    const hasS = dialogMaterialTypes.includes('S');
    const hasT = dialogMaterialTypes.includes('T');
    const otherLines = dialogSelectedLines.filter(line => line !== 'tl1' && line !== 'tl2');

    if (hasTL1 && hasS) addMaterialOption('tl1', `${formattedMaterial} S`);
    if (hasTL2 && hasT) addMaterialOption('tl2', `${formattedMaterial} T`);

    if (otherLines.length > 0) {
      otherLines.forEach(line => {
        if (dialogMaterialTypes.length === 0) {
          addMaterialOption(line, formattedMaterial);
        } else {
          dialogMaterialTypes.forEach(type => {
            addMaterialOption(line, `${formattedMaterial} ${type}`);
          });
        }
      });
    }

    setDialogNewMaterial('');
    setDialogSelectedLines([]);
    setDialogMaterialTypes([]);
    setState(prev => ({ ...prev, isAddMaterialDialogOpen: false }));
    toast.success(`Materiaal "${formattedMaterial}" is toegevoegd.`);
  };

  const handleDeleteMaterial = (line: ProductionLine, material: string) => {
    removeMaterialOption(line, material);
    toast.success(`Materiaal "${material}" verwijderd van ${line.toUpperCase()}.`);
  };

  return (
    <div className="animate-fade-in space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-faerch-blue">Materialen Beheer</h1>
        <div className="relative w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Filter materialen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-9"
          />
        </div>
      </div>

      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium">Materialen per Lijn</h2>
          <Dialog open={state.isAddMaterialDialogOpen} onOpenChange={(isOpen) => setState(prev => ({ ...prev, isAddMaterialDialogOpen: isOpen }))}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="btn-text-gradient border-faerch-blue text-faerch-blue hover:bg-faerch-blue/5"
                disabled={isFlyLocked}
              >
                <Plus className="h-4 w-4 mr-1" />
                Nieuw Materiaal Toevoegen
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Nieuw Materiaal Toevoegen</DialogTitle>
                <DialogDescription>
                  Selecteer de lijnen en voer de naam en type(s) van het nieuwe materiaal in.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                {/* Lijn Selectie */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Productielijnen</label>
                  <div className="grid grid-cols-3 gap-2">
                    {(['tl1', 'tl2', 'p1', 'p2', 'p3'] as ProductionLine[]).map((line) => (
                      <div key={line} className="flex items-center space-x-2">
                        <Checkbox
                          id={`line-${line}`}
                          checked={dialogSelectedLines.includes(line)}
                          onCheckedChange={(checked) => {
                            setDialogSelectedLines(prev => checked ? [...prev, line] : prev.filter(l => l !== line));
                          }}
                          disabled={isFlyLocked}
                        />
                        <Label htmlFor={`line-${line}`} className="text-sm font-normal">{line.toUpperCase()}</Label>
                      </div>
                    ))}
                  </div>
                </div>
                {/* Materiaal Input */}
                <div>
                  <label className="text-sm font-medium mb-1 block">Materiaal Naam</label>
                  <Input
                    type="text"
                    placeholder="Naam van het materiaal..."
                    value={dialogNewMaterial}
                    onChange={(e) => setDialogNewMaterial(e.target.value)}
                    disabled={isFlyLocked}
                  />
                </div>
                {/* Materiaal Type */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Materiaal Type (optioneel)</label>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="type-t" checked={dialogMaterialTypes.includes('T')} onCheckedChange={(checked) => setDialogMaterialTypes(prev => checked ? [...prev, 'T'] : prev.filter(t => t !== 'T'))} disabled={isFlyLocked} />
                      <Label htmlFor="type-t" className="text-sm font-normal">Type T</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="type-s" checked={dialogMaterialTypes.includes('S')} onCheckedChange={(checked) => setDialogMaterialTypes(prev => checked ? [...prev, 'S'] : prev.filter(t => t !== 'S'))} disabled={isFlyLocked} />
                      <Label htmlFor="type-s" className="text-sm font-normal">Type S</Label>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Speciale regel: S alleen voor TL1, T alleen voor TL2. Anders beide types toegevoegd indien aangevinkt.</p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setState(prev => ({ ...prev, isAddMaterialDialogOpen: false }))}>Annuleren</Button>
                <Button onClick={handleDialogAddMaterial} disabled={isFlyLocked}>Toevoegen</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {(['tl1', 'tl2', 'p1', 'p2', 'p3'] as ProductionLine[]).map((line) => {
              const lineMaterials = materialOptions[line] || [];
              const filteredMaterials = lineMaterials.filter(material =>
                material.toLowerCase().includes(searchTerm.toLowerCase())
              );
              return (
                <div key={line} className="border rounded-lg p-3 bg-gray-50/50">
                  <h3 className="text-sm font-semibold mb-2 text-center text-faerch-blue border-b pb-1">{line.toUpperCase()}</h3>
                  {filteredMaterials.length === 0 ? (
                    <p className="text-xs text-gray-400 italic text-center mt-2">
                      {searchTerm ? 'Geen match' : 'Geen materialen'}
                    </p>
                  ) : (
                    <ul className="space-y-1 mt-2">
                      {filteredMaterials.map((material, index) => (
                        <li key={`${line}-${material}-${index}`} className="flex justify-between items-center text-xs hover:bg-gray-100 rounded px-1 py-0.5">
                          <span className="flex-grow mr-1 truncate">{material}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteMaterial(line, material)}
                            className="text-red-500 hover:text-red-700 flex-shrink-0 h-5 w-5 p-0"
                            disabled={isFlyLocked}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </Card>
    </div>
  );
};

// Exporteer voor gebruik elders
// export default MaterialManagement; // Kan eventueel als default export 