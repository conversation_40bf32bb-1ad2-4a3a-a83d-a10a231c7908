
import { toast as sonnerToast } from 'sonner';

type ToastProps = {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive' | undefined;
};

// Individual toast functions that can be called directly
export const success = (props: ToastProps | string) => {
  const title = typeof props === 'string' ? props : props.title;
  const description = typeof props === 'string' ? undefined : props.description;
  
  sonnerToast.success(title, {
    description,
    position: 'bottom-right',
    duration: 3000,
  });
};

export const error = (props: ToastProps | string) => {
  const title = typeof props === 'string' ? props : props.title;
  const description = typeof props === 'string' ? undefined : props.description;
  
  sonnerToast.error(title, {
    description,
    position: 'bottom-right',
    duration: 4000,
  });
};

export const warning = (props: ToastProps | string) => {
  const title = typeof props === 'string' ? props : props.title;
  const description = typeof props === 'string' ? undefined : props.description;
  
  sonnerToast.warning(title, {
    description,
    position: 'bottom-right',
    duration: 3000,
  });
};

export const info = (props: ToastProps | string) => {
  const title = typeof props === 'string' ? props : props.title;
  const description = typeof props === 'string' ? undefined : props.description;
  
  sonnerToast.info(title, {
    description,
    position: 'bottom-right',
    duration: 3000,
  });
};

// Default toast supports additional parameters like variant
export const toastMessage = (props: ToastProps | string) => {
  if (typeof props === 'string') {
    sonnerToast(props, {
      position: 'bottom-right',
      duration: 3000,
    });
    return;
  }
  
  if (props.variant === 'destructive') {
    sonnerToast.error(props.title, {
      description: props.description,
      position: 'bottom-right',
      duration: 4000,
    });
  } else {
    sonnerToast(props.title, {
      description: props.description,
      position: 'bottom-right',
      duration: 3000,
    });
  }
};

// Re-export sonnerToast for direct access
export const toast = sonnerToast;

// Custom hook that returns toast functions
export function useToast() {
  return {
    toast: sonnerToast,
    success,
    error,
    warning,
    info,
    toastMessage
  };
}

// Export default for direct usage
export default sonnerToast;
