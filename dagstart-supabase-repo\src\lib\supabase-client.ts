import { createClient } from '@supabase/supabase-js';

// Supabase client setup
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

// Removed diagnostic logs

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL of API key ontbreekt in .env bestand'); // Reverted to original error message
}

export const supabase = createClient(supabaseUrl, supabaseKey);
