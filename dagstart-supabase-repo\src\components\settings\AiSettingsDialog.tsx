import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

// Define the type for AI settings directly here or import if shared
type AiSettings = {
  enabled: boolean;
  apiKey: string;
  prompt: string;
};

interface AiSettingsDialogProps {
  initialSettings: AiSettings;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (settings: AiSettings) => void;
}

const AiSettingsDialog: React.FC<AiSettingsDialogProps> = ({
  initialSettings,
  open,
  onOpenChange,
  onSave,
}) => {
  const [apiKey, setApiKey] = useState('');
  const [prompt, setPrompt] = useState('');

  useEffect(() => {
    if (open) {
      setApiKey(initialSettings.apiKey || '');
      setPrompt(initialSettings.prompt || '');
    }
  }, [open, initialSettings]);

  const handleSave = () => {
    onSave({
      ...initialSettings, // Keep the 'enabled' status
      apiKey: apiKey,
      prompt: prompt,
    });
    onOpenChange(false); // Close dialog
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>AI Configuratie</DialogTitle>
          <DialogDescription>
            Voer uw API Key en eventueel een aangepaste prompt in.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="ai-api-key-dialog">API Key</Label>
            <Input
              id="ai-api-key-dialog"
              type="password"
              placeholder="Voer uw AI API Key in"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
            />
            <p className="text-xs text-gray-500">Uw API key wordt lokaal opgeslagen.</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="ai-prompt-dialog">Aangepaste Prompt (Optioneel)</Label>
            <Textarea
              id="ai-prompt-dialog"
              placeholder="Voer een aangepaste prompt in om de AI te sturen..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={4}
            />
             <p className="text-xs text-gray-500">Laat leeg om de standaard prompt te gebruiken.</p>
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline">Annuleren</Button>
          </DialogClose>
          <Button type="button" onClick={handleSave}>Opslaan</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AiSettingsDialog;