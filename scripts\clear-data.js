// Script om alle notificaties en todo's te verwijderen uit de Supabase database
const { createClient } = require('@supabase/supabase-js');

// Supabase configuratie
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  console.error('Supabase URL is niet ingesteld. Stel REACT_APP_SUPABASE_URL in als omgevingsvariabele.');
  process.exit(1);
}

if (!supabaseKey) {
  console.error('Supabase API key is niet ingesteld. Stel REACT_APP_SUPABASE_ANON_KEY in als omgevingsvariabele.');
  process.exit(1);
}

// Supabase client initialiseren
const supabase = createClient(supabaseUrl, supabaseKey);

async function clearData() {
  console.log('Start met het verwijderen van alle notificaties en todo\'s...');

  try {
    // Verwijder alle notificaties
    const { error: notificationError, data: deletedNotifications } = await supabase
      .from('notifications')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Dummy conditie om alle rijen te verwijderen
      .select();

    if (notificationError) {
      console.error('Fout bij het verwijderen van notificaties:', notificationError);
    } else {
      console.log(`${deletedNotifications?.length || 0} notificaties verwijderd.`);
    }

    // Verwijder alle todo's
    const { error: todoError, data: deletedTodos } = await supabase
      .from('todos')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Dummy conditie om alle rijen te verwijderen
      .select();

    if (todoError) {
      console.error('Fout bij het verwijderen van todo\'s:', todoError);
    } else {
      console.log(`${deletedTodos?.length || 0} todo's verwijderd.`);
    }

    // Verwijder alle bestanden in de images bucket
    const { data: files, error: listError } = await supabase
      .storage
      .from('images')
      .list();

    if (listError) {
      console.error('Fout bij het ophalen van bestanden uit de images bucket:', listError);
    } else if (files && files.length > 0) {
      const filePaths = files.map(file => file.name);
      const { error: deleteError } = await supabase
        .storage
        .from('images')
        .remove(filePaths);

      if (deleteError) {
        console.error('Fout bij het verwijderen van bestanden uit de images bucket:', deleteError);
      } else {
        console.log(`${filePaths.length} bestanden verwijderd uit de images bucket.`);
      }
    } else {
      console.log('Geen bestanden gevonden in de images bucket.');
    }

    console.log('Alle data is succesvol verwijderd!');
  } catch (error) {
    console.error('Onverwachte fout bij het verwijderen van data:', error);
  }
}

// Voer de functie uit
clearData();
