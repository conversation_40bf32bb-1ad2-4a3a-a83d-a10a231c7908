import React, { useState, useMemo } from 'react';
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList, // Import CommandList
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { EquipmentEntry } from '@/types'; // Assuming EquipmentEntry has value/label or similar

interface EquipmentSelectorProps {
  id?: string;
  value: string; // The currently selected equipment value (e.g., 'EQP-001')
  onChange: (value: string) => void;
  // Changed to accept an array of EquipmentEntry directly
  equipmentOptions: EquipmentEntry[]; // e.g., [{ id: 'EQP-001', name: 'Mixer A' }, { id: 'EQP-002', name: 'Filler B' }]
  className?: string;
  placeholder?: string;
}

const EquipmentSelector: React.FC<EquipmentSelectorProps> = ({
  id,
  value,
  onChange,
  equipmentOptions,
  className,
  placeholder = "Selecteer onderdeel...",
}) => {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(""); // State for the command input text

  // Transform equipmentOptions (now an array) into the format needed by the Combobox
  const options = useMemo(() => {
    // Ensure equipmentOptions is an array before mapping
    if (!Array.isArray(equipmentOptions)) {
        return [];
    }
    // Map to include id, label, and the display value (entry.value)
    return equipmentOptions.map((entry) => ({
      id: entry.id, // Keep id as the unique key/value for selection logic
      label: String(entry.label_nl || entry.id), // Use label_nl for display text
      displayCode: entry.value || '', // Store the desired code (e.g., "1-376") for display
    }));
  }, [equipmentOptions]);

  // Ensure selectedLabel is treated as a string or fallback
  // Find the label for the current value, if it exists in options
  // Find the selected option based on the id (which is stored in the 'value' prop)
  const selectedOption = useMemo(() =>
    options.find((option) => option.id?.toLowerCase() === value?.toLowerCase()), // Compare against option.id
    [options, value]
  );
  const displayValue = selectedOption ? selectedOption.label : value; // Show label if found, else show raw value

  return (
    <Popover open={open} onOpenChange={(isOpen) => {
      setOpen(isOpen);
      // If closing and there's an input value that wasn't selected from the list
      if (!isOpen && inputValue) {
        // Check if input matches label, displayCode, or id of any option
        const matchingOption = options.find(option =>
          option.label.toLowerCase() === inputValue.toLowerCase() ||
          option.displayCode.toLowerCase() === inputValue.toLowerCase() ||
          option.id.toLowerCase() === inputValue.toLowerCase()
        );
        // If the input doesn't match any known option, use the input value directly
        if (!matchingOption) {
          onChange(inputValue);
        }
        // Reset input value when closing, regardless of match
        // setInputValue(""); // Keep input value for display if needed, or clear if preferred
      }
       // Clear input when opening
       if (isOpen) {
         setInputValue("");
       }
    }}>
      <PopoverTrigger asChild>
        <Button
          id={id}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between font-normal", className)} // Ensure button takes full width like input
        >
          {value ? displayValue : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
        {/* Add filter prop to Command for custom filtering if needed, default works well */}
        {/* Update filter to check label and displayCode */}
        <Command filter={(itemValue, search) => {
            // itemValue here is option.id
            const option = options.find(opt => opt.id.toLowerCase() === itemValue.toLowerCase());
            if (!option) return 0;

            const searchTerm = search.toLowerCase();
            // Check label
            if (option.label.toLowerCase().includes(searchTerm)) {
                return 1;
            }
            // Check display code
            if (option.displayCode.toLowerCase().includes(searchTerm)) {
                return 1;
            }
            // Check id (original itemValue)
             if (itemValue.toLowerCase().includes(searchTerm)) {
                 return 1;
             }
            return 0; // No match
        }}>
          <CommandInput
            placeholder="Zoek of typ onderdeel..."
            value={inputValue}
            onValueChange={setInputValue} // Update local input state
           />
          <CommandList> {/* Wrap items in CommandList for scrolling */}
            <CommandEmpty>Geen onderdeel gevonden.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.id}
                  value={option.id} // Use id for the CommandItem value (used in filter and onSelect)
                  onSelect={(currentValue) => {
                    // Find the actual value corresponding to the selected item's display text/value
                    // When an item is selected from the list
                    // currentValue is the id of the selected CommandItem
                    const selectedId = options.find(opt => opt.id.toLowerCase() === currentValue.toLowerCase())?.id || "";
                    onChange(selectedId); // Update the main value with the ID
                    setInputValue(""); // Clear the input field
                    setOpen(false); // Close the popover
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value?.toLowerCase() === option.id.toLowerCase() ? "opacity-100" : "opacity-0" // Compare against id
                    )}
                  />
                  {/* Display label and the displayCode (e.g., "1-376") */}
                  {option.label} ({option.displayCode})
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default EquipmentSelector;