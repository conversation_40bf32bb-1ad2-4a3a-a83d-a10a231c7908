import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useProduction } from '@/context/ProductionContext';
import { ProductionLine, EquipmentEntry } from '@/types';
import LineSelector from '@/components/common/LineSelector';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from '@/components/ui/accordion';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger
} from '@/components/ui/dialog';
import { Plus, Trash, Edit, Save, X, Loader2, Upload, FileText } from 'lucide-react';
import { toast } from 'sonner';
import * as equipmentUtils from '@/utils/equipmentUtils';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav';
import { Tabs } from '@/components/ui/tabs';
import { translateText } from '@/lib/translation';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  CalendarDays, ClockIcon, PlusCircle, Trash2, Check, Calendar as CalendarIcon,
  Pencil, ChevronUp, ChevronDown, ChevronsUpDown, Check as CheckIcon
} from 'lucide-react';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

const EquipmentCodes: React.FC = () => {
  const { 
    equipmentOptions, 
    updateEquipmentOptions,
    addEquipmentOption: contextAddEquipmentOption,
    removeEquipmentOption: contextRemoveEquipmentOption,
    editEquipmentOption: contextEditEquipmentOption,
    addEquipmentArea: contextAddEquipmentArea,
    removeEquipmentArea: contextRemoveEquipmentArea,
    editEquipmentArea: contextEditEquipmentArea,
    isFlyLocked
  } = useProduction();

  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [openAreaDialog, setOpenAreaDialog] = useState(false);
  const [openEquipmentDialog, setOpenEquipmentDialog] = useState(false);
  const [openEditAreaDialog, setOpenEditAreaDialog] = useState(false);
  const [openEditEquipmentDialog, setOpenEditEquipmentDialog] = useState(false);
  const [selectedArea, setSelectedArea] = useState<string | null>(null);
  const [selectedEquipment, setSelectedEquipment] = useState<EquipmentEntry | null>(null);
  
  // Form states
  const [newAreaCode, setNewAreaCode] = useState('');
  const [newAreaLabel, setNewAreaLabel] = useState('');
  const [newEquipmentCode, setNewEquipmentCode] = useState('');
  const [newEquipmentLabelNL, setNewEquipmentLabelNL] = useState('');
  const [newEquipmentLabelEN, setNewEquipmentLabelEN] = useState('');
  const [isAddingEquipment, setIsAddingEquipment] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  
  // Current line's equipment options
  const lineEquipment = useMemo(() => {
    return equipmentOptions[selectedLine] || {};
  }, [equipmentOptions, selectedLine]);
  
  // Function to add a new equipment area
  const handleAddArea = () => {
    // Valideer alleen op label/beschrijving
    if (!newAreaLabel) {
      toast.error('Vul de gebied beschrijving in');
      return;
    }
    
    // We gebruiken de beschrijving nu als de 'code' (key) in het equipmentOptions object.
    // Controleer of dit gebied (label) al bestaat voor deze lijn.
    if (lineEquipment[newAreaLabel]) { 
      toast.error('Dit gebied bestaat al voor deze lijn');
      return;
    }
    
    // Gebruik context functie indien beschikbaar. Geef label als code én label.
    if (contextAddEquipmentArea) {
      contextAddEquipmentArea(selectedLine, newAreaLabel, newAreaLabel);
    } else {
      // Pas fallback aan om label als code te gebruiken
      const updatedOptions = equipmentUtils.addEquipmentArea(
        selectedLine, 
        newAreaLabel, // Gebruik label als code
        newAreaLabel, 
        equipmentOptions
      );
      updateEquipmentOptions(selectedLine, updatedOptions[selectedLine]);
    }
    
    toast.success(`Gebied ${newAreaLabel} toegevoegd`);
    // Reset alleen label state
    setNewAreaLabel('');
    setOpenAreaDialog(false);
  };
  
  // Function to add a new equipment
  const handleAddEquipment = async () => {
    if (!selectedArea || !newEquipmentCode || !newEquipmentLabelNL) {
      toast.error('Vul alle verplichte velden in');
      return;
    }
    
    const exists = lineEquipment[selectedArea]?.some(
      eq => eq.value === newEquipmentCode
    );
    
    if (exists) {
      toast.error('Deze onderdeel code bestaat al');
      return;
    }

    setIsAddingEquipment(true);

    let translatedLabelEN = newEquipmentLabelNL;
    try {
      translatedLabelEN = await translateText(newEquipmentLabelNL, 'en');
    } catch (error) {
      console.error("Error during translation:", error);
      toast.error("Vertaling mislukt, Engelse naam is hetzelfde als Nederlands.");
      translatedLabelEN = newEquipmentLabelNL;
    }
    
    const newEquipment: EquipmentEntry = {
      id: `${Date.now()}`,
      value: newEquipmentCode,
      label_nl: newEquipmentLabelNL,
      label_en: translatedLabelEN
    };
    
    if (contextAddEquipmentOption) {
      contextAddEquipmentOption(selectedLine, selectedArea, newEquipment);
    } else {
      const updatedOptions = equipmentUtils.addEquipmentOption(
        selectedLine, 
        selectedArea, 
        newEquipment, 
        equipmentOptions
      );
      updateEquipmentOptions(selectedLine, updatedOptions[selectedLine]);
    }
    
    toast.success(`Onderdeel ${newEquipmentCode} toegevoegd`);
    setNewEquipmentCode('');
    setNewEquipmentLabelNL('');
    setNewEquipmentLabelEN('');
    setOpenEquipmentDialog(false);
    setIsAddingEquipment(false);
  };
  
  // Function to remove an equipment area
  const handleRemoveArea = (areaCode: string) => {
    if (confirm(`Weet je zeker dat je gebied ${areaCode} wilt verwijderen? Alle onderdelen in dit gebied wordt ook verwijderd.`)) {
      // Use context function if available, otherwise use fallback
      if (contextRemoveEquipmentArea) {
        contextRemoveEquipmentArea(selectedLine, areaCode);
      } else {
        const updatedOptions = equipmentUtils.removeEquipmentArea(
          selectedLine, 
          areaCode, 
          equipmentOptions
        );
        updateEquipmentOptions(selectedLine, updatedOptions[selectedLine]);
      }
      
      toast.success(`Gebied ${areaCode} verwijderd`);
    }
  };
  
  // Function to remove an equipment
  const handleRemoveEquipment = (areaCode: string, equipment: EquipmentEntry) => {
    if (confirm(`Weet je zeker dat je onderdeel ${equipment.value} wilt verwijderen?`)) {
      // Use context function if available, otherwise use fallback
      if (contextRemoveEquipmentOption) {
        contextRemoveEquipmentOption(selectedLine, areaCode, equipment.id);
      } else {
        const updatedOptions = equipmentUtils.removeEquipmentOption(
          selectedLine, 
          areaCode, 
          equipment.id, 
          equipmentOptions
        );
        updateEquipmentOptions(selectedLine, updatedOptions[selectedLine]);
      }
      
      toast.success(`Onderdeel ${equipment.value} verwijderd`);
    }
  };
  
  // Dialog handlers
  const openAddAreaDialog = () => {
    setNewAreaCode('');
    setNewAreaLabel('');
    setOpenAreaDialog(true);
  };
  
  const openAddEquipmentDialog = (areaCode: string) => {
    setSelectedArea(areaCode);
    setNewEquipmentCode('');
    setNewEquipmentLabelNL('');
    setNewEquipmentLabelEN('');
    setOpenEquipmentDialog(true);
  };
  
  // Edit area handlers
  const openEditAreaDialogHandler = (areaCode: string) => {
    setSelectedArea(areaCode);
    setNewAreaCode(areaCode);
    setNewAreaLabel(areaCode); // Ideally this would be the area label, but we don't store it separately
    setOpenEditAreaDialog(true);
  };
  
  const handleEditArea = () => {
    if (!selectedArea || !newAreaCode) {
      toast.error('Gebied code is verplicht');
      return;
    }
    
    if (selectedArea !== newAreaCode && lineEquipment[newAreaCode]) {
      toast.error('Deze gebiedscode bestaat al');
      return;
    }
    
    // Use context function if available, otherwise use fallback
    if (contextEditEquipmentArea) {
      contextEditEquipmentArea(selectedLine, selectedArea, newAreaCode, newAreaLabel);
    } else {
      const updatedOptions = equipmentUtils.editEquipmentArea(
        selectedLine, 
        selectedArea, 
        newAreaCode, 
        newAreaLabel, 
        equipmentOptions
      );
      updateEquipmentOptions(selectedLine, updatedOptions[selectedLine]);
    }
    
    toast.success(`Gebied ${selectedArea} bijgewerkt`);
    setSelectedArea(null);
    setNewAreaCode('');
    setNewAreaLabel('');
    setOpenEditAreaDialog(false);
  };
  
  // Edit equipment handlers
  const openEditEquipmentDialogHandler = (areaCode: string, equipment: EquipmentEntry) => {
    setSelectedArea(areaCode);
    setSelectedEquipment(equipment);
    setNewEquipmentCode(equipment.value);
    setNewEquipmentLabelNL(equipment.label_nl);
    setNewEquipmentLabelEN(equipment.label_en);
    setOpenEditEquipmentDialog(true);
  };
  
  const handleEditEquipment = () => {
    if (!selectedArea || !selectedEquipment || !newEquipmentCode || !newEquipmentLabelNL) {
      toast.error('Vul alle verplichte velden in');
      return;
    }
    
    // Check if equipment with this code already exists (except the current one)
    const exists = lineEquipment[selectedArea]?.some(
      eq => eq.value === newEquipmentCode && eq.id !== selectedEquipment.id
    );
    
    if (exists) {
      toast.error('Deze onderdeel code bestaat al');
      return;
    }
    
    const updatedEquipment: EquipmentEntry = {
      ...selectedEquipment,
      value: newEquipmentCode,
      label_nl: newEquipmentLabelNL,
      label_en: newEquipmentLabelEN || newEquipmentLabelNL
    };
    
    // Use context function if available, otherwise use fallback
    if (contextEditEquipmentOption) {
      contextEditEquipmentOption(selectedLine, selectedArea, selectedEquipment.id, updatedEquipment);
    } else {
      const updatedOptions = equipmentUtils.editEquipmentOption(
        selectedLine, 
        selectedArea, 
        selectedEquipment.id,
        updatedEquipment, 
        equipmentOptions
      );
      updateEquipmentOptions(selectedLine, updatedOptions[selectedLine]);
    }
    
    toast.success(`Onderdeel ${newEquipmentCode} bijgewerkt`);
    setSelectedArea(null);
    setSelectedEquipment(null);
    setNewEquipmentCode('');
    setNewEquipmentLabelNL('');
    setNewEquipmentLabelEN('');
    setOpenEditEquipmentDialog(false);
  };
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        setSelectedFile(file);
      } else {
        toast.error("Selecteer a.u.b. een geldig .csv bestand.");
        setSelectedFile(null);
        event.target.value = ''; // Reset file input
      }
    } else {
      setSelectedFile(null);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Necessary to allow drop
    setIsDraggingOver(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDraggingOver(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDraggingOver(false);
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
       if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
         setSelectedFile(file);
         toast.info(`Bestand geselecteerd: ${file.name}`);
       } else {
         toast.error("Ongeldig bestandstype. Sleep a.u.b. een .csv bestand.");
         setSelectedFile(null);
       }
      event.dataTransfer.clearData();
    }
  };

  // Basic CSV Parser (Assumes simple CSV, no commas within quotes)
  const parseCSV = (text: string): Record<string, string>[] => {
    const lines = text.trim().split(/\r?\n/);
    if (lines.length < 2) return []; // Need header + at least one data row
    
    const header = lines[0].split(',').map(h => h.trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length === header.length) {
        const row: Record<string, string> = {};
        header.forEach((col, index) => {
          row[col] = values[index];
        });
        data.push(row);
      }
    }
    return data;
  };

  const handleUpload = () => {
    if (!selectedFile) {
      toast.error("Selecteer eerst een bestand.");
      return;
    }

    setIsUploading(true);
    const reader = new FileReader();

    reader.onload = (event) => {
      const text = event.target?.result as string;
      if (!text) {
        toast.error("Kon het bestand niet lezen.");
        setIsUploading(false);
        return;
      }

      try {
        const parsedData = parseCSV(text);
        if (parsedData.length === 0) {
           toast.warning("CSV is leeg of kon niet geparsed worden.");
           setIsUploading(false);
           return;
        }

        // Validate header
        const requiredHeaders = ['Line', 'Area', 'EquipmentCode', 'EquipmentNameNL'];
        const header = Object.keys(parsedData[0]);
        const missingHeaders = requiredHeaders.filter(h => !header.includes(h));
        if (missingHeaders.length > 0) {
          toast.error(`CSV mist verplichte kolommen: ${missingHeaders.join(', ')}`);
          setIsUploading(false);
          return;
        }
        
        let addedCount = 0;
        let errorCount = 0;
        
        // Process each row
        parsedData.forEach((row, index) => {
          const line = row.Line as ProductionLine | undefined;
          const area = row.Area;
          const code = row.EquipmentCode; // This is the 'value'
          const labelNL = row.EquipmentNameNL;
          const labelEN = row.EquipmentNameEN || labelNL; // Use NL as fallback for EN

          if (!line || !area || !code || !labelNL) {
            console.warn(`Skipping row ${index + 2}: Missing required data.`);
            errorCount++;
            return; // Skip row if required data is missing
          }

          // Convert line to lowercase and trim whitespace for case-insensitive comparison
          const processedLine = line.trim().toLowerCase(); 

          if (!['tl1', 'tl2', 'p1', 'p2', 'p3'].includes(processedLine)) {
             console.warn(`Skipping row ${index + 2}: Invalid line '${line}'. Allowed: tl1, tl2, p1, p2, p3.`);
             errorCount++;
             return; // Skip row if line is invalid
          }
          
          try {
             // Check if area exists for the line, if not, add it
             // Note: addEquipmentArea expects (line, areaCode, areaLabel)
             // We use 'area' as both code and label here.
             if (!equipmentOptions[processedLine] || !equipmentOptions[processedLine][area]) {
                contextAddEquipmentArea(processedLine as ProductionLine, area, area); // Use processedLine
                console.log(`Area '${area}' toegevoegd aan lijn '${processedLine}'.`);
             }

             // Check if equipment code already exists in this area for this line
             const equipmentExists = equipmentOptions[processedLine]?.[area]?.some(eq => eq.value === code); // Use processedLine
             
             if (!equipmentExists) {
                 const newEquipment: EquipmentEntry = {
                    id: crypto.randomUUID(),
                    value: code,
                    label_nl: labelNL,
                    label_en: labelEN
                 };
                 contextAddEquipmentOption(processedLine as ProductionLine, area, newEquipment); // Use processedLine
                 addedCount++;
             } else {
                 console.warn(`Skipping row ${index + 2}: Equipment code '${code}' bestaat al in gebied '${area}' voor lijn '${processedLine}'.`);
                 errorCount++;
             }
          } catch (e) {
              console.error(`Error processing row ${index + 2}:`, e);
              errorCount++;
          }
        });
        
        if (addedCount > 0) {
           toast.success(`${addedCount} onderdeel/onderdelen succesvol toegevoegd.`);
        }
        if (errorCount > 0) {
           toast.warning(`${errorCount} rij(en) konden niet worden verwerkt (zie console voor details).`);
        }
        if (addedCount === 0 && errorCount === 0) {
             toast.info("Geen nieuwe onderdelen gevonden om toe te voegen.");
        }

      } catch (parseError) {
        console.error("Error parsing CSV:", parseError);
        toast.error("Fout bij het parsen van het CSV bestand.");
      } finally {
        setIsUploading(false);
        setSelectedFile(null);
        setUploadDialogOpen(false);
      }
    };

    reader.onerror = () => {
      toast.error("Fout bij het lezen van het bestand.");
      setIsUploading(false);
      setSelectedFile(null);
    };

    reader.readAsText(selectedFile);
  };

  // Function to reset upload state when dialog closes
  const handleUploadDialogChange = (open: boolean) => {
     setUploadDialogOpen(open);
     if (!open) {
         setSelectedFile(null);
         setIsDraggingOver(false);
         setIsUploading(false); // Reset upload status as well
     }
  }

  return (
    <div className="p-6 space-y-6">
      <Tabs value="equipment">
        <SettingsSubNav />
      </Tabs>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-faerch-blue">Apparaat Beheer</h1>
        <div className="flex items-center space-x-4">
          <div className="flex space-x-2">
            <Button onClick={openAddAreaDialog} variant="outline" size="sm" disabled={isFlyLocked}>
               <Plus className="h-4 w-4 mr-2" /> Nieuw Gebied
            </Button>
            <Dialog open={uploadDialogOpen} onOpenChange={handleUploadDialogChange}>
               <DialogTrigger asChild>
                  <Button variant="outline" size="sm" disabled={isFlyLocked}>
                     <Upload className="h-4 w-4 mr-2" /> Importeer CSV
                  </Button>
               </DialogTrigger>
               <DialogContent className="sm:max-w-2xl">
                  <DialogHeader>
                     <DialogTitle>Importeer Onderdelen vanuit CSV</DialogTitle>
                     <DialogDescription>
                        Upload een CSV-bestand om meerdere gebieden en onderdelen tegelijk toe te voegen.
                     </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                     <div>
                        <h3 className="text-md font-medium mb-2">CSV Formaat Vereisten</h3>
                        <p className="text-sm text-muted-foreground mb-1">
                           Het CSV-bestand moet de volgende kolommen bevatten (met exact deze namen in de eerste rij/header):
                        </p>
                        <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1 pl-4 bg-muted/50 p-3 rounded-md">
                           <li><code>Line</code>: Productielijn (tl1, tl2, p1, p2, of p3)</li>
                           <li><code>Area</code>: Naam van het gebied/sectie</li>
                           <li><code>EquipmentCode</code>: Unieke code van het onderdeel (wordt de 'value')</li>
                           <li><code>EquipmentNameNL</code>: Nederlandse naam/label</li>
                           <li><code>EquipmentNameEN</code>: Engelse naam/label (optioneel, laat leeg indien niet beschikbaar)</li>
                        </ul>
                     </div>
                     
                     {/* --- CSV Voorbeeld --- */}
                     <div className="mt-4">
                        <h4 className="text-sm font-medium mb-1">Voorbeeld:</h4>
                        <pre className="text-xs bg-muted/50 p-3 rounded-md overflow-x-auto"><code>{`Line,Area,EquipmentCode,EquipmentNameNL,EquipmentNameEN
tl1,Hotwash,1-010,Invoerband,Input Belt`}</code></pre>
                     </div>
                     {/* --- Einde CSV Voorbeeld --- */}

                     {/* --- Drag and Drop Area --- */}
                     <div 
                        className={`mt-4 border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${isDraggingOver ? 'border-primary bg-primary/10' : 'border-border hover:border-primary/50'}`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                     >
                        <input 
                           id="dialog-csv-upload-input"
                           type="file" 
                           accept=".csv"
                           onChange={handleFileChange}
                           className="hidden"
                        />
                        <label 
                           htmlFor="dialog-csv-upload-input" 
                           className="cursor-pointer w-full flex flex-col items-center justify-center"
                        >
                           <FileText className={`h-12 w-12 mb-3 ${isDraggingOver ? 'text-primary' : 'text-muted-foreground'}`} />
                           {selectedFile ? (
                              <p className="text-sm font-medium">Geselecteerd: {selectedFile.name}</p>
                           ) : (
                              <p className="text-sm text-muted-foreground">
                                 Sleep uw .csv bestand hierheen, of <span className="font-medium text-primary">klik om te selecteren</span>.
                              </p>
                           )}
                           
                        </label>
                     </div>
                  </div>
                  <DialogFooter>
                     <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>Annuleren</Button>
                     <Button 
                        onClick={handleUpload} 
                        disabled={!selectedFile || isUploading || isFlyLocked}
                     >
                        {isUploading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                        Upload en Verwerk
                     </Button>
                  </DialogFooter>
               </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <div className="dashboard-card">
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-xl font-semibold">Onderdeel Codes</h2>
          </div>
          
          <div className="p-4">
            <LineSelector 
              selectedLine={selectedLine} 
              onChange={setSelectedLine} 
              className="mb-4" 
            />
            
            {Object.keys(lineEquipment).length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <p>Geen gebieden gevonden. Voeg een nieuw gebied toe om te beginnen.</p>
              </div>
            ) : (
              <Accordion type="multiple" className="space-y-4">
                {Object.entries(lineEquipment).map(([areaCode, equipmentList]: [string, EquipmentEntry[]]) => (
                  <AccordionItem key={areaCode} value={areaCode} className="border rounded-lg">
                    <AccordionTrigger className="px-4 py-3 flex justify-between items-center w-full hover:bg-gray-50">
                      <div className="flex items-center">
                        <span className="font-semibold">{areaCode}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 py-2">
                      <div className="flex justify-between items-center mb-4">
                        <div className="flex space-x-2">
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => openEditAreaDialogHandler(areaCode)}
                            disabled={isFlyLocked}
                          >
                            <Edit className="h-4 w-4 mr-1 text-blue-500" /> Bewerk Gebied
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => handleRemoveArea(areaCode)}
                            disabled={isFlyLocked}
                          >
                            <Trash className="h-4 w-4 mr-1 text-red-500" /> Verwijder Gebied
                          </Button>
                        </div>
                        <Button size="sm" onClick={() => openAddEquipmentDialog(areaCode)} disabled={isFlyLocked}>
                          <Plus className="h-4 w-4 mr-1" /> Nieuw Onderdeel
                        </Button>
                      </div>
                      
                      {Array.isArray(equipmentList) && equipmentList.length === 0 ? (
                        <p className="text-gray-500 italic">Geen onderdelen gevonden in dit gebied</p>
                      ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {Array.isArray(equipmentList) && equipmentList.map((equipment) => (
                            <div
                              key={equipment.id}
                              className="border rounded-md p-3 bg-gray-50 flex flex-col"
                            >
                              <div className="flex-grow">
                                <p className="font-medium text-sm">{equipment.value}</p>
                                <p className="text-xs text-gray-600">
                                  NL: {equipment.label_nl}
                                  {equipment.label_en && ` / EN: ${equipment.label_en}`}
                                </p>
                              </div>
                              <div className="flex justify-end space-x-1 mt-2">
                                <Button 
                                  variant="ghost" 
                                  onClick={() => openEditEquipmentDialogHandler(areaCode, equipment)}
                                  disabled={isFlyLocked}
                                >
                                  <Edit className="h-4 w-4 text-blue-500" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  onClick={() => handleRemoveEquipment(areaCode, equipment)}
                                  disabled={isFlyLocked}
                                >
                                  <Trash className="h-4 w-4 text-red-500" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </div>
        </div>
      </div>
      
      {/* Dialog for adding a new area */}
      <Dialog open={openAreaDialog} onOpenChange={setOpenAreaDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuw Gebied</DialogTitle>
            <DialogDescription>
              Voeg een nieuw gebied toe voor {selectedLine.toUpperCase()}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium" htmlFor="area-label">Gebied Beschrijving</label>
              <Input
                id="area-label"
                value={newAreaLabel}
                onChange={(e) => setNewAreaLabel(e.target.value)}
                placeholder="bijv. Voeding & Initiële Sortering"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenAreaDialog(false)}>
              Annuleren
            </Button>
            <Button onClick={handleAddArea} disabled={isFlyLocked}>
              Toevoegen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Dialog for adding new equipment */}
      <Dialog open={openEquipmentDialog} onOpenChange={setOpenEquipmentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuw Onderdeel</DialogTitle>
            <DialogDescription>
              Voeg nieuw onderdeel toe aan gebied {selectedArea}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium" htmlFor="equipment-code">Onderdeel Code</label>
              <Input
                id="equipment-code"
                value={newEquipmentCode}
                onChange={(e) => setNewEquipmentCode(e.target.value)}
                placeholder="bijv. 2-010-01"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium" htmlFor="equipment-label-nl">Nederlandse Naam</label>
              <Input
                id="equipment-label-nl"
                value={newEquipmentLabelNL}
                onChange={(e) => setNewEquipmentLabelNL(e.target.value)}
                placeholder="Naam in het Nederlands"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenEquipmentDialog(false)} disabled={isAddingEquipment}>
              Annuleren
            </Button>
            <Button onClick={handleAddEquipment} disabled={isAddingEquipment || isFlyLocked}>
              {isAddingEquipment ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Toevoegen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Dialog for editing an area */}
      <Dialog open={openEditAreaDialog} onOpenChange={setOpenEditAreaDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bewerk Gebied</DialogTitle>
            <DialogDescription>
              Bewerk het gebied {selectedArea}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium" htmlFor="edit-area-code">Gebied Code</label>
              <Input
                id="edit-area-code"
                value={newAreaCode}
                onChange={(e) => setNewAreaCode(e.target.value)}
                placeholder="bijv. 2-010"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium" htmlFor="edit-area-label">Gebied Beschrijving</label>
              <Input
                id="edit-area-label"
                value={newAreaLabel}
                onChange={(e) => setNewAreaLabel(e.target.value)}
                placeholder="bijv. Voeding & Initiële Sortering"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenEditAreaDialog(false)}>
              Annuleren
            </Button>
            <Button onClick={handleEditArea} disabled={isFlyLocked}>
              Opslaan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Dialog for editing equipment */}
      <Dialog open={openEditEquipmentDialog} onOpenChange={setOpenEditEquipmentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bewerk Onderdeel</DialogTitle>
            <DialogDescription>
              Bewerk onderdeel {selectedEquipment?.value} in gebied {selectedArea}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium" htmlFor="edit-equipment-code">Onderdeel Code</label>
              <Input
                id="edit-equipment-code"
                value={newEquipmentCode}
                onChange={(e) => setNewEquipmentCode(e.target.value)}
                placeholder="bijv. 2-010-01"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium" htmlFor="edit-equipment-label-nl">Nederlandse Naam</label>
              <Input
                id="edit-equipment-label-nl"
                value={newEquipmentLabelNL}
                onChange={(e) => setNewEquipmentLabelNL(e.target.value)}
                placeholder="Naam in het Nederlands"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenEditEquipmentDialog(false)}>
              Annuleren
            </Button>
            <Button onClick={handleEditEquipment} disabled={isFlyLocked}>
              Opslaan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EquipmentCodes;
