@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Styling for Lucide icons */
  svg.lucide {
    /* Fixed size with !important to prevent overrides */
    height: 1.5rem !important;
    width: 1.5rem !important;
    min-height: 1.5rem !important;
    min-width: 1.5rem !important;
    max-height: 1.5rem !important;
    max-width: 1.5rem !important;
    /* Preserve aspect ratio */
    aspect-ratio: 1 / 1 !important;
    /* Allow transformations for positioning */
    /* transform: none !important; - removed to allow positioning */
    transition: none !important;
    /* Prevent pointer events */
    pointer-events: none !important;
    /* Ensure visibility */
    opacity: 1 !important;
    visibility: visible !important;
    /* Prevent filters */
    filter: none !important;
    /* Set a fixed stroke width */
    stroke-width: 2px !important;
  }

  /* Special exception for search icons in input fields */
  .relative svg.lucide-search {
    height: 1rem !important;
    width: 1rem !important;
    min-height: 1rem !important;
    min-width: 1rem !important;
    max-height: 1rem !important;
    max-width: 1rem !important;
    /* Allow transform for proper positioning */
    transform: translateY(-50%) !important;
  }

  /* Navigation icons - use solid colors instead of gradients */
  .modern-nav-link svg.lucide,
  nav a svg.lucide,
  header a svg.lucide,
  [class*="navigation"] svg.lucide {
    stroke: #3b82f6 !important; /* Solid blue color */
  }

  /* Button icons */
  button svg.lucide:not(.text-white):not(.text-red-600):not(.text-destructive):not(.text-destructive-foreground) {
    stroke: #4b5563 !important; /* Solid gray color */
  }

  /* Special size overrides for navigation */
  header a svg.lucide,
  .navigation-menu svg.lucide {
    height: 1.5rem !important;
    width: 1.5rem !important;
    min-height: 1.5rem !important;
    min-width: 1.5rem !important;
  }

  /* Special size overrides for mobile menu */
  .mobile-menu svg.lucide {
    height: 1.25rem !important;
    width: 1.25rem !important;
    min-height: 1.25rem !important;
    min-width: 1.25rem !important;
  }

  html {
    height: 100%;
    margin: 0; /* Add margin reset */
    padding: 0; /* Add padding reset */
  }

  body {
    @apply text-foreground antialiased;
    height: 100%; /* Change from min-height: 100vh */
    position: relative;
    margin: 0; /* Add margin reset */
    padding: 0; /* Add padding reset */
  }

  /* Ensure the root element also takes full height */
  #root {
    height: 100%;
  }

  @media (min-width: 1920px) {
    html {
      font-size: 18px;
    }
  }

  @media (min-width: 2560px) {
    html {
      font-size: 20px;
    }
  }

  @media (min-width: 3840px) {
    html {
      font-size: 24px;
    }
  }
}

@layer components {
  .dashboard-card {
    @apply bg-white/85 backdrop-blur-md border border-white/20
    rounded-2xl shadow-sm hover:shadow-md
    transition-all duration-300 ease-in-out;
  }

  .dashboard-input {
    @apply bg-white/90 backdrop-blur-sm border border-white/20
    rounded-xl px-3 py-2 shadow-sm hover:shadow
    focus:outline-none focus:ring-2 focus:ring-primary/20
    focus:border-primary/40 transition-all;
  }

  .target-reached {
    @apply text-green-600 font-medium;
  }

  .target-missed {
    @apply text-red-500 font-medium;
  }

  .transition-material {
    @apply bg-gradient-to-r from-amber-100 to-amber-200 italic;
  }

  .faerch-gradient {
    @apply bg-gradient-to-r from-faerch-blue to-faerch-teal text-white;
  }

  .modern-nav-link {
    @apply flex items-center px-3 py-2 rounded-xl text-gray-700
    transition-all duration-300
    hover:bg-white/75 font-medium relative overflow-hidden;
  }

  .modern-nav-link-active {
    @apply bg-white/90 text-primary font-semibold shadow-sm;
  }

  /* Touch-friendly navigation styles */
  .navigation-menu-content {
    @apply absolute top-full mt-2 animate-in fade-in-0 zoom-in-95 z-50
    rounded-2xl shadow-sm border border-white/20 bg-white/85 backdrop-blur-md
    overflow-hidden;
  }

  .navigation-menu-content[data-state="open"] {
    @apply animate-in fade-in-0 zoom-in-95;
  }

  .navigation-menu-content[data-state="closed"] {
    @apply animate-out fade-out-0 zoom-out-95;
  }

  /* Hover styles only for non-touch devices */
  @media (hover: hover) {
    .modern-nav-link:hover::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: theme('colors.primary.DEFAULT');
      transform: scaleX(1);
      transform-origin: bottom left;
      transition: transform 0.3s ease;
    }
  }

  /* Active state for touch devices */
  @media (hover: none) {
    .modern-nav-link:active,
    .navigation-menu-trigger:active {
      @apply bg-gray-100;
    }
  }

  .modern-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: theme('colors.primary.DEFAULT');
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
  }

  .nav-button {
    @apply relative py-2 px-3 text-white transition-all duration-300
           hover:bg-white/10 rounded-md flex items-center gap-2
           before:absolute before:bottom-0 before:left-1/2 before:-translate-x-1/2
           before:w-0 before:h-0.5 before:bg-white before:transition-all before:duration-300
           hover:before:w-4/5;
  }

  .nav-button.active {
    @apply bg-white/10 before:w-4/5;
  }

  .h-chart-md {
    height: 320px;
    max-height: 25vh;
  }

  .h-chart-lg {
    height: 400px;
    max-height: 30vh;
  }

  @media (min-width: 2560px) {
    .h-chart-md {
      height: 280px;
      max-height: 22vh;
    }

    .h-chart-lg {
      height: 350px;
      max-height: 25vh;
    }
  }

  @media (width: 3440px) {
    .h-chart-md {
      height: 260px;
      max-height: 20vh;
    }

    .h-chart-lg {
      height: 320px;
      max-height: 22vh;
    }
  }

  .hover-scale-102:hover {
    transform: scale(1.02);
  }

  .btn-flashy {
    background: linear-gradient(45deg, #60a5fa, #3b82f6, #2563eb);
    background-size: 200% 200%;
    color: white;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
  }

  .btn-flashy:hover {
    background-position: right center;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
  }

  .btn-soft {
    background: linear-gradient(135deg, #93c5fd, #60a5fa, #3b82f6);
    background-size: 200% 200%;
    color: white;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.1);
  }

  .btn-soft:hover {
    background-position: right center;
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(37, 99, 235, 0.2);
  }

  .btn-text-gradient {
    background: linear-gradient(45deg, #1f2937, #4b5563, #9ca3af);
    -webkit-background-clip: text;
    background-clip: text;
    background-size: 200% auto;
    transition: all 0.3s ease;
    position: relative;
    border: none;
    padding: 0.5rem 1rem;
  }

  .btn-text-gradient svg.lucide {
    /* Restore gradient for icons */
    stroke: url(#btn-gradient) !important;
    /* Maintain size constraints */
    min-height: 1rem;
    min-width: 1rem;
  }

  /* Ensure SVG gradients are properly applied */
  @supports (stroke: url(#gradient)) {
    /* Ensure gradient definitions are available */
    svg[style*="position: absolute"] {
      z-index: -1;
    }
  }

  .btn-text-gradient::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 100%;
    height: 2px;
    background: linear-gradient(45deg, #1f2937, #4b5563, #9ca3af);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
  }

  .btn-text-gradient:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }

  .btn-text-gradient:hover {
    background-position: right center;
  }

  .btn-text-gradient.active {
    background: linear-gradient(45deg, #1f2937, #4b5563, #9ca3af);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .btn-text-gradient.active::after {
    background: linear-gradient(45deg, #9ca3af, #d1d5db, #e5e7eb);
    transform: scaleX(1);
  }

  .btn-glow {
    @apply relative shadow-none hover:shadow-[0_0_15px_rgba(0,150,255,0.5)]
           transition-all duration-500;
  }

  .ripple {
    @apply relative overflow-hidden;
  }

  .ripple::after {
    @apply content-[''] block absolute top-1/2 left-1/2 w-[300%] h-[300%]
           bg-white/30 rounded-full -translate-x-1/2 -translate-y-1/2
           scale-0 opacity-0 transition-all duration-500;
  }

  .ripple:active::after {
    @apply scale-100 opacity-100 transition-none;
  }

  /* Submenu styling */
  .submenu-container {
    @apply bg-white/85 backdrop-blur-md rounded-2xl
    border border-white/20 shadow-sm
    transition-all duration-300;
  }

  .submenu-item {
    @apply px-3 py-2 rounded-xl hover:bg-white/90
    transition-all duration-300;
  }

  /* Button styling */
  .custom-button {
    @apply rounded-xl shadow-sm hover:shadow-md
    transition-all duration-300 ease-in-out;
  }

  /* Select styling */
  .select-container {
    @apply rounded-xl shadow-sm hover:shadow-md
    transition-all duration-300 bg-white/80 backdrop-blur-sm;
  }

  /* Dialog styling */
  .dialog-content {
    @apply rounded-2xl shadow-lg bg-white/90 backdrop-blur-md
    border border-white/20;
  }

  /* Content container styling */
  .content-container {
    @apply bg-white/85 backdrop-blur-md rounded-2xl
    border border-white/20 shadow-sm
    transition-all duration-300;
  }

  /* Header styling */
  .main-header {
    @apply bg-white/85 backdrop-blur-md border-b border-white/20
    shadow-sm transition-all duration-300;
  }

  /* Menu container styling */
  .menu-container {
    @apply bg-white/85 backdrop-blur-md rounded-2xl
    border border-white/20 shadow-sm p-4
    transition-all duration-300;
  }

  /* White backdrop styling */
  .white-backdrop {
    @apply bg-white/85 backdrop-blur-md rounded-2xl
    border border-white/20 shadow-sm
    transition-all duration-300;
  }

  .btn-report {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn-report::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-report:hover {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .btn-report:hover::before {
    opacity: 1;
  }

  .btn-report .text-gradient {
    background: linear-gradient(45deg, #1a1a1a, #4a4a4a);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    background-size: 200% auto;
    transition: all 0.3s ease;
  }

  .btn-report:hover .text-gradient {
    background-position: right center;
    background: linear-gradient(45deg, #2a2a2a, #6a6a6a);
    -webkit-background-clip: text;
    background-clip: text;
  }

  .btn-nav-style {
    background: transparent;
    position: relative;
    transition: all 0.3s ease;
  }

  .btn-nav-style span {
    background: linear-gradient(45deg, #1f2937, #4b5563, #9ca3af);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    background-size: 200% auto;
  }

  .btn-nav-style::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 100%;
    height: 2px;
    background: linear-gradient(45deg, #9ca3af, #d1d5db, #e5e7eb);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
  }

  .btn-nav-style:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }

  .btn-nav-style:hover span {
    background-position: right center;
  }

  .btn-nav-style.active span {
    background: linear-gradient(45deg, #1f2937, #4b5563, #9ca3af);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .btn-nav-style.active::after {
    transform: scaleX(1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 120, 200, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(0, 120, 200, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 120, 200, 0); }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideInRight 0.5s ease-out;
}

.animate-pulse-shadow {
  animation: pulse 2s infinite;
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

/* Nederlands datumformaat voor input[type="date"] */
input[type="date"] {
  position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 0;
  padding-right: 0.5rem;
  cursor: pointer;
}

/* Verberg de standaard datumweergave */
input[type="date"]::-webkit-datetime-edit {
  display: none;
}

/* Toon het aangepaste formaat */
input[type="date"]::before {
  content: attr(data-date);
  display: inline-block;
  color: currentColor;
}

/* Toon placeholder wanneer er geen datum is geselecteerd */
input[type="date"]:not([data-date])::before {
  content: "Selecteer een datum";
  color: #6b7280;
}

/* Zorg ervoor dat de input de juiste grootte behoudt */
input[type="date"] {
  min-width: 200px;
  padding: 0.5rem;
}

/* Navigatie gradient styles */
.text-gradient {
  background: linear-gradient(45deg, #2563eb, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% auto;
}

.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(45deg, #2563eb, #3b82f6, #60a5fa);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.nav-link:hover::before {
  transform: translateX(0);
}

.nav-link:hover {
  transform: translateY(-2px);
}

.bg-gradient-to-r {
  background-size: 200% auto;
  transition: all 0.3s ease;
}

.bg-gradient-to-r:hover {
  background-position: right center;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.hover\:text-gradient:hover {
  background: linear-gradient(45deg, #2563eb, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% auto;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

