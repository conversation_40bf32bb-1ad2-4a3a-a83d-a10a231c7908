
import { v4 as uuidv4 } from 'uuid';
import { 
  ProductionLine, 
  LineData, 
  ProductionRow, 
  Disruption, 
  DisruptionInput,
  EquipmentEntry
} from '@/types';
import { defaultProductionData } from '@/types/production-context';

// Function to create an empty production row
export const createEmptyProductionRow = (
  line: ProductionLine, 
  date: string, 
  target: number, 
  materialOptions: string[], 
  customRow?: Partial<ProductionRow>
): ProductionRow => {
  return {
    id: uuidv4(), // Add unique ID
    date,
    // line, // Removed non-existent property
    // target, // Removed non-existent property
    od: {
      production: 0,
      material: materialOptions[0] || '',
      // isTransition: false, // Removed non-existent property
      yield: 0,
      target: target / 3
    },
    md: {
      production: 0,
      material: materialOptions[0] || '',
      // isTransition: false, // Removed non-existent property
      yield: 0,
      target: target / 3
    },
    nd: {
      production: 0,
      material: materialOptions[0] || '',
      // isTransition: false, // Removed non-existent property
      yield: 0,
      target: target / 3
    },
    // breakdowns property removed from ProductionRow type
    // breakdowns: [ ... ],
    ...customRow
  };
};

// Function to update equipment options with a new option
export const addEquipmentOptionToData = (
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>,
  line: ProductionLine,
  areaCode: string,
  option: EquipmentEntry
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  const linePrev = equipmentOptions[line] || {};
  const areaOptions = linePrev[areaCode] || [];
  
  return {
    ...equipmentOptions,
    [line]: {
      ...linePrev,
      [areaCode]: [...areaOptions, option]
    }
  };
};

// Function to remove equipment option from data
export const removeEquipmentOptionFromData = (
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>,
  line: ProductionLine,
  areaCode: string,
  optionId: string
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  const linePrev = equipmentOptions[line] || {};
  const areaOptions = linePrev[areaCode] || [];
  
  return {
    ...equipmentOptions,
    [line]: {
      ...linePrev,
      [areaCode]: areaOptions.filter(option => option.id !== optionId)
    }
  };
};

// Function to edit equipment option in data
export const editEquipmentOptionInData = (
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>,
  line: ProductionLine,
  areaCode: string,
  optionId: string,
  updatedOption: EquipmentEntry
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  const linePrev = equipmentOptions[line] || {};
  const areaOptions = linePrev[areaCode] || [];
  
  return {
    ...equipmentOptions,
    [line]: {
      ...linePrev,
      [areaCode]: areaOptions.map(option => 
        option.id === optionId ? updatedOption : option
      )
    }
  };
};

// Function to add equipment area to data
export const addEquipmentAreaToData = (
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>,
  line: ProductionLine,
  areaCode: string,
  areaLabel: string // Add missing parameter
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  const linePrev = equipmentOptions[line] || {};
  
  return {
    ...equipmentOptions,
    [line]: {
      ...linePrev,
      // Use areaCode and potentially areaLabel if needed, though label isn't stored here
      [areaCode]: []
    }
  };
};

// Function to remove equipment area from data
export const removeEquipmentAreaFromData = (
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>,
  line: ProductionLine,
  areaCode: string
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  const linePrev = { ...equipmentOptions[line] };
  delete linePrev[areaCode];
  
  return {
    ...equipmentOptions,
    [line]: linePrev
  };
};

// Function to edit equipment area in data
export const editEquipmentAreaInData = (
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>,
  line: ProductionLine,
  areaCode: string,
  updatedAreaCode: string,
  updatedAreaLabel: string // Add missing parameter
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  const linePrev = { ...equipmentOptions[line] };
  const areaOptions = linePrev[areaCode] || [];
  delete linePrev[areaCode];
  
  return {
    ...equipmentOptions,
    [line]: {
      ...linePrev,
      // Use updatedAreaCode and potentially updatedAreaLabel if needed
      [updatedAreaCode]: areaOptions
    }
  };
};
