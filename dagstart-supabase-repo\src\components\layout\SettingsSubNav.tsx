import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Target, Package, Info, Clock, Settings, History, Construction, SlidersHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useProduction } from '@/context/ProductionContext'; // Import context hook
import { Switch } from "@/components/ui/switch"; // Import Switch
import { Label } from "@/components/ui/label"; // Import Label

export const SettingsSubNav = ({ className }: { className?: string }) => {
  const location = useLocation();
  const navigate = useNavigate();
  // Get Fly Lock state and setter from context
  const { isFlyLocked, setIsFlyLocked } = useProduction(); 

  // Bepaal actieve tab op basis van huidige pad
  let activeTab = 'targets'; // Default
  if (location.pathname.startsWith('/settings/materials')) {
    activeTab = 'materials';
  } else if (location.pathname.startsWith('/settings/equipment')) {
    activeTab = 'equipment';
  } else if (location.pathname.startsWith('/settings/toggles')) {
    activeTab = 'toggles';
  } else if (location.pathname.startsWith('/history')) { // Check history route
    activeTab = 'history'; // Use 'history' value for consistency with HistoryV2 page
  } else if (location.pathname.startsWith('/settings')) { // Check algemeen /settings
     activeTab = 'targets'; // Targets is nu de default voor /settings
  }

  // Functie om naar de juiste settings-subpagina of history te navigeren
  const handleNavigate = (value: string) => {
    if (value === 'history') {
      navigate('/history');
    } else {
      navigate(`/settings/${value}`);
    }
  };

  return (
    <div className="flex items-center justify-between border-b pb-2 mb-4">
      <nav className="flex items-center space-x-4 lg:space-x-6">
        <TabsList className={cn("grid w-full grid-cols-5 mb-6", className)}> {/* Verhoogd naar 5 kolommen */}
          <TabsTrigger 
            value="targets" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('targets')}
            data-state={activeTab === 'targets' ? 'active' : 'inactive'}
          >
            <Target className="h-4 w-4" />
            Target & Yields
          </TabsTrigger>
          <TabsTrigger 
            value="materials" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('materials')}
            data-state={activeTab === 'materials' ? 'active' : 'inactive'}
          >
            <Package className="h-4 w-4" />
            Materialen
          </TabsTrigger>
          <TabsTrigger 
            value="equipment" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('equipment')}
            data-state={activeTab === 'equipment' ? 'active' : 'inactive'}
          >
            <Construction className="h-4 w-4" />
            Apparaat beheer
          </TabsTrigger>
          <TabsTrigger 
            value="history" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('history')}
            data-state={activeTab === 'history' ? 'active' : 'inactive'}
          >
            <History className="h-4 w-4" />
            Geschiedenis
          </TabsTrigger>
          <TabsTrigger 
            value="toggles" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('toggles')}
            data-state={activeTab === 'toggles' ? 'active' : 'inactive'}
          >
            <SlidersHorizontal className="h-4 w-4" />
            Toggles
          </TabsTrigger>
        </TabsList>
      </nav>
      {/* Fly Lock Switch verwijderen omdat deze nu in de Toggles pagina staat */}
      <div className="flex items-center space-x-2">
      </div>
    </div>
  );
}; 