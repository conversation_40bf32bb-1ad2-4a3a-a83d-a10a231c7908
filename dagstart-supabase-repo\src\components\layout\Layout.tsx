import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON>, 
  AlertCircle, 
  ClipboardList, 
  FileText, 
  History, 
  Menu, 
  X,
  Package,
  Info,
  Settings,
  ChevronDown,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList } from '@/components/ui/navigation-menu';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const isMobile = useIsMobile();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileSubMenuOpen, setMobileSubMenuOpen] = useState(false);
  
  const toggleMobileMenu = () => {
    setMobileMenuOpen(prev => !prev);
    if (mobileMenuOpen) {
       setMobileSubMenuOpen(false);
    }
  }
  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    setMobileSubMenuOpen(false);
  }
  const toggleMobileSubMenu = () => setMobileSubMenuOpen(prev => !prev);

  const isSettingsPage = location.pathname.startsWith('/settings') || location.pathname === '/history';
  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen">
      <div className="container mx-auto py-4 px-4 md:px-6">
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
          <header className="border-b border-gray-200">
            <div className="flex h-16 items-center justify-between px-4">
              <div className="flex items-center space-x-4">
                <img 
                  src="/logo.png" 
                  alt="Cirrec Logo" 
                  className="h-10 w-auto drop-shadow-sm" 
                />
              </div>

              {!isMobile && (
                <div className="flex-grow flex justify-center">
                  <NavigationMenu>
                    <NavigationMenuList>
                      <NavigationMenuItem>
                        <Link
                          to="/safety-quality"
                          className={cn(
                            "px-6 py-3 text-lg font-medium rounded-md transition-all duration-300 btn-text-gradient",
                            location.pathname === '/safety-quality' && "active"
                          )}
                        >
                          Veiligheid & Kwaliteit
                        </Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link
                          to="/results"
                          className={cn(
                            "px-6 py-3 text-lg font-medium rounded-md transition-all duration-300 btn-text-gradient",
                            location.pathname === '/results' && "active"
                          )}
                        >
                          Dag Start
                        </Link>
                      </NavigationMenuItem>
                      {/* Restored Storingen Link */}
                      <NavigationMenuItem>
                        <Link
                          to="/disruptions"
                          className={cn(
                            "px-6 py-3 text-lg font-medium rounded-md transition-all duration-300 btn-text-gradient",
                            location.pathname === '/disruptions' && "active"
                          )}
                        >
                          Storingen
                        </Link>
                      </NavigationMenuItem>
                      {/* Removed Rapport Link */}
                      <NavigationMenuItem>
                        <Link
                          to="/overdracht" // New Link
                          className={cn(
                            "px-6 py-3 text-lg font-medium rounded-md transition-all duration-300 btn-text-gradient",
                            location.pathname === '/overdracht' && "active" // Active state for new link
                          )}
                        >
                          Overdracht
                        </Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link
                          to="/settings"
                          className={cn(
                            "px-6 py-3 text-lg font-medium rounded-md flex items-center gap-2 transition-all duration-300 btn-text-gradient",
                            isSettingsPage && "active"
                          )}
                        >
                          Instellingen
                        </Link>
                      </NavigationMenuItem>
                    </NavigationMenuList>
                  </NavigationMenu>
                </div>
              )}

              {isMobile && (
                <button 
                  className="p-2 rounded-md text-primary hover:bg-gray-100/50 transition-colors"
                  onClick={toggleMobileMenu}
                >
                  {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
                </button>
              )}
            </div>
          </header>

          {isMobile && mobileMenuOpen && (
            <div className="fixed inset-0 z-40 bg-black bg-opacity-50">
              <div className="fixed top-16 left-0 right-0 bg-white shadow-lg animate-fade-in">
                <nav className="flex flex-col p-4">
                  <Link 
                    to="/safety-quality" 
                    className={cn(
                      "modern-nav-link mb-2",
                      isActive('/safety-quality') ? "modern-nav-link-active" : ""
                    )}
                    onClick={closeMobileMenu}
                  >
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Veiligheid & Kwaliteit
                  </Link>
                  <Link 
                    to="/" 
                    className={cn(
                      "modern-nav-link mb-2",
                      isActive('/') || isActive('/results') ? "modern-nav-link-active" : ""
                    )}
                    onClick={closeMobileMenu}
                  >
                    <LineChart className="w-4 h-4 mr-2" />
                    Dashboard
                  </Link>
                  <Link 
                    to="/disruptions" 
                    className={cn(
                      "modern-nav-link mb-2",
                      isActive('/disruptions') ? "modern-nav-link-active" : ""
                    )}
                    onClick={closeMobileMenu}
                  >
                    <ClipboardList className="w-4 h-4 mr-2" />
                    Lopende Storingen
                  </Link>
                  {/* Removed Rapport Link */}
                  <button 
                    className={cn(
                      "modern-nav-link flex justify-between items-center w-full",
                      (isSettingsPage || mobileSubMenuOpen) ? "modern-nav-link-active" : ""
                    )}
                    onClick={toggleMobileSubMenu}
                  >
                    <span className="flex items-center">
                      <Settings className="w-4 h-4 mr-2" />
                      Instellingen
                    </span>
                  </button>

                  {mobileSubMenuOpen && (
                    <div className="pl-4 mt-2 border-l-2 border-gray-100 ml-2">
                      <Link
                        to="/settings"
                        className={cn(
                          "modern-nav-link text-sm mb-2",
                          location.pathname === '/settings' || location.pathname === '/settings/targets' ? "text-primary font-medium" : "text-gray-600"
                        )}
                        onClick={closeMobileMenu}
                      >
                        <Target className="h-4 w-4 mr-2" />
                        Targets & Yields
                      </Link>
                      <Link
                        to="/settings/materials"
                        className={cn(
                          "modern-nav-link text-sm mb-2",
                          isActive('/settings/materials') ? "text-primary font-medium" : "text-gray-600"
                        )}
                        onClick={closeMobileMenu}
                      >
                        <Package className="h-4 w-4 mr-2" />
                        Materialen
                      </Link>
                      <Link
                        to="/settings/equipment"
                        className={cn(
                          "modern-nav-link text-sm mb-2",
                          isActive('/settings/equipment') ? "text-primary font-medium" : "text-gray-600"
                        )}
                        onClick={closeMobileMenu}
                      >
                        <Info className="h-4 w-4 mr-2" />
                        Apparatuur
                      </Link>
                      <Link
                        to="/history"
                        className={cn(
                          "modern-nav-link text-sm",
                          isActive('/history') ? "text-primary font-medium" : "text-gray-600"
                        )}
                        onClick={closeMobileMenu}
                      >
                        <History className="w-4 w-4 mr-2" />
                        Geschiedenis
                      </Link>
                    </div>
                  )}
                </nav>
              </div>
            </div>
          )}

          <main className="flex-grow p-6">
            <div className="animate-fade-in h-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default Layout;
