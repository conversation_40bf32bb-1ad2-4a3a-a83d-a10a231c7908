import React from 'react';
import { Outlet } from 'react-router-dom';
import { Tabs } from '@/components/ui/tabs';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav';
import { MaterialsSubNav } from '@/components/materials/MaterialsSubNav';

const Materials: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <Tabs defaultValue="materials">
        <SettingsSubNav />
      </Tabs>

      <Tabs defaultValue="management">
        <MaterialsSubNav />
        <div className="mt-6">
          <Outlet />
        </div>
      </Tabs>
    </div>
  );
};

export default Materials;
